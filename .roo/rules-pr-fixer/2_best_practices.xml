<best_practices>
  <general_principles>
    <principle priority="high">
      <name>Context is Key</name>
      <description>Always gather full context before attempting a fix. This includes reading all relevant PR comments, checking CI/CD logs, and understanding the surrounding code.</description>
      <rationale>Without full context, fixes may be incomplete or introduce new issues.</rationale>
    </principle>
    <principle priority="medium">
      <name>Incremental Fixes</name>
      <description>Address issues one at a time (e.g., fix tests first, then address comments). This makes the process more manageable and easier to validate.</description>
      <rationale>Tackling all issues at once can be complex and error-prone.</rationale>
    </principle>
    <principle priority="high">
      <name>Handle Fork Remotes Correctly</name>
      <description>Always check if a PR comes from a fork (cross-repository) before pushing changes. Use 'gh pr view --json isCrossRepository' to determine the correct remote.</description>
      <rationale>Pushing to the wrong remote (e.g., origin instead of fork) will fail for cross-repository PRs.</rationale>
      <example>
        <scenario>PR from a fork</scenario>
        <good>Check isCrossRepository, add fork remote if needed, push to fork</good>
        <bad>Always push to origin without checking PR source</bad>
      </example>
    </principle>
    <principle priority="high">
      <name>Safe File Staging</name>
      <description>Always review files before staging to avoid committing temporary files, build artifacts, or system files. Use selective git commands that respect .gitignore.</description>
      <rationale>Committing unwanted files can expose sensitive data, clutter the repository, and cause CI/CD failures.</rationale>
      <example>
        <scenario>Staging files for commit</scenario>
        <good>Use 'git add -u' to stage only modified tracked files, or explicitly list files to add</good>
        <bad>Use 'git add .' which stages everything including temp files</bad>
      </example>
      <checklist>
        <item>Review git status before staging</item>
        <item>Check for temporary files (.swp, .DS_Store, *.tmp)</item>
        <item>Exclude build artifacts (dist/, build/, *.pyc)</item>
        <item>Avoid IDE-specific files (.idea/, .vscode/)</item>
        <item>Verify .gitignore is properly configured</item>
      </checklist>
    </principle>
  </general_principles>

  <code_conventions>
    <convention category="merge_conflicts">
      <rule>Delegate merge conflict resolution to the merge-resolver mode.</rule>
      <template>
When merge conflicts are detected, do not attempt to resolve them manually. Instead, use the new_task tool to create a task for the merge-resolver mode:

```xml
<new_task>
<mode>merge-resolver</mode>
<message>#[PR_NUMBER]</message>
</new_task>
```

The merge-resolver mode will:
- Checkout the PR branch
- Perform the rebase
- Intelligently resolve conflicts based on commit history and intent
- Push the resolved changes
- Return control back to pr-fixer mode

This ensures consistent and intelligent conflict resolution across all PRs.
      </template>
    </convention>
  </code_conventions>

  <quality_checklist>
    <category name="before_completion">
      <item>Have all review comments been addressed?</item>
      <item>Are all CI/CD checks passing?</item>
      <item>Is the PR free of merge conflicts?</item>
      <item>Have the changes been tested locally?</item>
    </category>
  </quality_checklist>
</best_practices>