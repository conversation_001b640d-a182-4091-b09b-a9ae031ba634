<common_patterns>
  <pattern name="checking_pr_status">
    <usage>A set of commands to quickly assess the state of a Pull Request.</usage>
    <template>
      <command tool="gh">
        gh pr status --json number,title,state,conflict,reviewDecision,headRefName,headRepositoryOwner
      </command>
      <command tool="gh">
        gh pr checks
      </command>
      <command tool="gh">
        gh pr view --comments
      </command>
    </template>
  </pattern>
  <pattern name="analyzing_failing_tests">
    <usage>Commands to investigate why a specific test is failing.</usage>
    <template>
      <command tool="gh">
        gh run list --workflow=<workflow_id> --branch=<branch_name> --json databaseId,name,status,conclusion
      </command>
      <command tool="gh">
        gh run view --log-failed <run_id>
      </command>
    </template>
  </pattern>
  <pattern name="detecting_conflicts">
    <usage>Commands to detect merge conflicts.</usage>
    <template>
      <comment>Check PR mergeable status</comment>
      <command tool="gh">gh pr view <pr_number> --json mergeable,mergeStateStatus</command>
      <comment>If mergeable is false or mergeStateStatus is CONFLICTING, delegate to merge-resolver</comment>
    </template>
  </pattern>
  
  <pattern name="delegating_conflict_resolution">
    <usage>Delegate merge conflict resolution to the merge-resolver mode.</usage>
    <template>
      <comment>When conflicts are detected, create a new task for merge-resolver</comment>
      <command tool="new_task"><![CDATA[
<new_task>
<mode>merge-resolver</mode>
<message>#<pr_number></message>
</new_task>
      ]]></command>
      <comment>Wait for merge-resolver to complete before continuing with other fixes</comment>
    </template>
  </pattern>
  
  <pattern name="checking_out_pr">
    <usage>Check out a pull request branch locally.</usage>
    <template>
      <command tool="gh">gh pr checkout <pr_number_or_url> --force</command>
      <comment>Alternative if gh checkout fails:</comment>
      <command tool="git">git fetch origin pull/<pr_number>/head:<branch_name> && git checkout <branch_name></command>
    </template>
  </pattern>
  
  <pattern name="determine_push_remote">
    <usage>Determine the correct remote to push to (handles forks).</usage>
    <template>
      <comment>Get PR metadata to check if it's from a fork</comment>
      <command tool="gh">gh pr view <pr_number> --json headRepositoryOwner,headRefName,isCrossRepository</command>
      <comment>If isCrossRepository is true, it's from a fork</comment>
      <command tool="git">git remote -v</command>
      <comment>Check if fork remote exists, otherwise add it</comment>
      <command tool="git">git remote add fork https://github.com/<fork_owner>/<repo_name>.git</command>
      <comment>Use appropriate remote based on PR source</comment>
    </template>
  </pattern>
  
  <pattern name="real_time_monitoring">
    <usage>Monitor PR checks in real-time as they run.</usage>
    <template>
      <command tool="gh">gh pr checks <pr_number> --watch</command>
      <comment>Continuously monitor check status with automatic updates</comment>
      <alternative>For one-time status check: gh pr checks <pr_number> --json state,conclusion,name,detailsUrl</alternative>
      <command tool="gh">gh run list --pr <pr_number> --json databaseId,status,conclusion</command>
    </template>
  </pattern>
  
  <pattern name="safe_push_operations">
    <usage>Push operations that handle both origin and fork remotes correctly.</usage>
    <template>
      <comment>First determine the correct remote (origin or fork)</comment>
      <command tool="gh">gh pr view <pr_number> --json headRepositoryOwner,headRefName,isCrossRepository</command>
      <comment>If isCrossRepository is false, push to origin</comment>
      <command tool="git">git push --force-with-lease origin <branch_name></command>
      <comment>If isCrossRepository is true, push to fork remote</comment>
      <command tool="git">git push --force-with-lease fork <branch_name></command>
      <comment>If force-with-lease fails, fetch and retry</comment>
      <command tool="git">git fetch <remote> <branch_name></command>
      <command tool="git">git push --force <remote> <branch_name></command>
    </template>
  </pattern>

  <pattern name="automated_commit_operations">
    <usage>Commit operations that work in automated environments while respecting .gitignore.</usage>
    <template>
      <comment>Review what files have been modified</comment>
      <command tool="git">git status --porcelain</command>
      <comment>Add only tracked files that were modified (respects .gitignore)</comment>
      <command tool="git">git add -u</command>
      <comment>If you need to add specific new files, list them explicitly</comment>
      <command tool="git">git add <specific_file_path></command>
      <command tool="git">git commit -m "<commit_message>"</command>
    </template>
  </pattern>
  <pattern name="safe_file_staging">
    <usage>Safely stage files for commit while avoiding temporary files and respecting .gitignore.</usage>
    <template>
      <comment>First, check what files are currently modified or untracked</comment>
      <command tool="git">git status --porcelain</command>
      <comment>Review the output to identify files that should NOT be committed:</comment>
      <comment>- Files starting with . (hidden files like .DS_Store, .swp)</comment>
      <comment>- Build artifacts (dist/, build/, *.pyc, *.o)</comment>
      <comment>- IDE files (.idea/, .vscode/, *.iml)</comment>
      <comment>- Temporary files (*.tmp, *.temp, *~)</comment>
      
      <comment>Option 1: Stage only modified tracked files (safest)</comment>
      <command tool="git">git add -u</command>
      
      <comment>Option 2: Stage specific files by path</comment>
      <command tool="git">git add src/file1.ts src/file2.ts</command>
      
      <comment>Option 3: Use pathspec to add files matching a pattern</comment>
      <command tool="git">git add '*.ts' '*.tsx' --</command>
      
      <comment>Option 4: Interactive staging to review each change</comment>
      <command tool="git">git add -p</command>
      
      <comment>Always verify what's staged before committing</comment>
      <command tool="git">git diff --cached --name-only</command>
    </template>
  </pattern>
</common_patterns>
