// Roomote Agent 相关类型定义

export interface JiraConfig {
	url: string
	username: string
	password: string
}

export interface JiraIssue {
	key: string
	id: string
	summary: string
	description: string
	status: {
		name: string
		id: string
		category: string
	}
	priority: {
		name: string
		id: string
	}
	assignee: {
		displayName: string
		emailAddress: string
		accountId: string
	} | null
	reporter: {
		displayName: string
		emailAddress: string
		accountId: string
	}
	project: {
		key: string
		name: string
	}
	issueType: {
		name: string
		id: string
	}
	created: string
	updated: string
	dueDate: string | null
	labels: string[]
	components: Array<{
		name: string
		id: string
	}>
	fixVersions: Array<{
		name: string
		id: string
	}>
}

export interface GitLabConfig {
	url: string
	token: string
}

export interface GitLabProject {
	id: number
	name: string
	path: string
	path_with_namespace: string
	description: string
	default_branch: string
	web_url: string
	namespace: {
		id: number
		name: string
		path: string
	}
}

export interface GitLabBranch {
	name: string
	commit: {
		id: string
		short_id: string
		title: string
		message: string
		author_name: string
		author_email: string
		authored_date: string
		committer_name: string
		committer_email: string
		committed_date: string
	}
	merged: boolean
	protected: boolean
	developers_can_push: boolean
	developers_can_merge: boolean
	can_push: boolean
	default: boolean
}

export interface RoomoteTaskPayload {
	type: "custom" | "jira.gitlab"
	customInstructions?: string
	jira?: {
		config: JiraConfig
		issue: JiraIssue
	}
	gitlab: {
		config: GitLabConfig
		project: GitLabProject
		branch: string
	}
	apiProvider?: string
	modelName?: string
}

export interface RoomoteJobResponse {
	success: boolean
	jobId?: string
	enqueuedJobId?: string
	error?: string
}

export interface RoomoteConnectionState {
	jira: {
		connected: boolean
		config: JiraConfig | null
		lastError: string | null
	}
	gitlab: {
		connected: boolean
		config: GitLabConfig | null
		projects: GitLabProject[]
		selectedProject: GitLabProject | null
		branches: GitLabBranch[]
		selectedBranch: string | null
		lastError: string | null
	}
}
