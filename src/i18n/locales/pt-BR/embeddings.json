{"unknownError": "<PERSON><PERSON>conhe<PERSON>", "authenticationFailed": "Falha ao criar embeddings: Falha na autenticação. Verifique sua chave de API.", "failedWithStatus": "Falha ao criar embeddings após {{attempts}} tentativas: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Falha ao criar embeddings após {{attempts}} tentativas: {{errorMessage}}", "failedMaxAttempts": "<PERSON>alha ao criar embeddings após {{attempts}} tentativas", "textExceedsTokenLimit": "O texto no índice {{index}} excede o limite máximo de tokens ({{itemTokens}} > {{maxTokens}}). Ignorando.", "rateLimitRetry": "Limite de taxa atingido, tentando novamente em {{delayMs}}ms (tentativa {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Não foi possível ler o corpo do erro", "requestFailed": "Solicitação da API Ollama falhou com status {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Estrutura de resposta inválida da API Ollama: array \"embeddings\" não encontrado ou não é um array.", "embeddingFailed": "Embedding Ollama falhou: {{message}}", "serviceNotRunning": "O serviço Ollama não está em execução em {{baseUrl}}", "serviceUnavailable": "O serviço Ollama não está disponível (status: {{status}})", "modelNotFound": "Modelo Ollama não encontrado: {{modelId}}", "modelNotEmbeddingCapable": "O modelo Ollama não é capaz de embedding: {{modelId}}", "hostNotFound": "Host Ollama não encontrado: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Erro desconhecido ao processar arquivo {{filePath}}", "unknownErrorDeletingPoints": "Erro desconhecido ao deletar pontos para {{filePath}}", "failedToProcessBatchWithError": "Falha ao processar lote após {{maxRetries}} tentativas: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Falha ao conectar com o banco de dados vetorial Qdrant. Certifique-se de que o Qdrant esteja rodando e acessível em {{qdrantUrl}}. Erro: {{errorMessage}}", "vectorDimensionMismatch": "Falha ao atualizar o índice de vetores para o novo modelo. Tente limpar o índice e começar novamente. Detalhes: {{errorMessage}}"}, "validation": {"authenticationFailed": "Falha na autenticação. Verifique sua chave de API nas configurações.", "connectionFailed": "Falha ao conectar ao serviço do embedder. Verifique suas configurações de conexão e garanta que o serviço esteja em execução.", "modelNotAvailable": "O modelo especificado não está disponível. Verifique a configuração do seu modelo.", "configurationError": "Configuração do embedder inválida. Revise suas configurações.", "serviceUnavailable": "O serviço do embedder não está disponível. Garanta que ele esteja em execução e acessível.", "invalidEndpoint": "Endpoint de API inválido. Verifique sua configuração de URL.", "invalidEmbedderConfig": "Configuração do embedder inválida. Verifique suas configurações.", "invalidApiKey": "Chave de API inválida. Verifique sua configuração de chave de API.", "invalidBaseUrl": "URL base inválida. Verifique sua configuração de URL.", "invalidModel": "Modelo inválido. Verifique a configuração do seu modelo.", "invalidResponse": "Resposta inválida do serviço de embedder. Verifique sua configuração.", "apiKeyRequired": "A chave de API é necessária para este embedder", "baseUrlRequired": "A URL base é necessária para este embedder"}, "serviceFactory": {"openAiConfigMissing": "Configuração do OpenAI ausente para criação do embedder", "ollamaConfigMissing": "Configuração do Ollama ausente para criação do embedder", "openAiCompatibleConfigMissing": "Configuração compatível com OpenAI ausente para criação do embedder", "geminiConfigMissing": "Configuração do Gemini ausente para criação do embedder", "mistralConfigMissing": "Configuração do Mistral ausente para a criação do embedder", "invalidEmbedderType": "Tipo de embedder configurado inválido: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Não foi possível determinar a dimensão do vetor para o modelo '{{modelId}}' com o provedor '{{provider}}'. Certifique-se de que a 'Dimensão de Embedding' esteja configurada corretamente nas configurações do provedor compatível com OpenAI.", "vectorDimensionNotDetermined": "Não foi possível determinar a dimensão do vetor para o modelo '{{modelId}}' com o provedor '{{provider}}'. Verifique os perfis do modelo ou a configuração.", "qdrantUrlMissing": "URL do Qdrant ausente para criação do armazenamento de vetores", "codeIndexingNotConfigured": "Não é possível criar serviços: A indexação de código não está configurada corretamente"}, "orchestrator": {"indexingFailedNoBlocks": "Indexação falhou: Nenhum bloco de código foi indexado com sucesso. Isso geralmente indica um problema de configuração do embedder.", "indexingFailedCritical": "Indexação falhou: Nenhum bloco de código foi indexado com sucesso apesar de encontrar arquivos para processar. Isso indica uma falha crítica do embedder.", "fileWatcherStarted": "Monitor de arquivos iniciado.", "fileWatcherStopped": "Monitor de arquivos parado.", "failedDuringInitialScan": "Fal<PERSON> durante a varredura inicial: {{errorMessage}}", "unknownError": "<PERSON><PERSON>conhe<PERSON>", "indexingRequiresWorkspace": "A indexação requer uma pasta de workspace aberta"}}