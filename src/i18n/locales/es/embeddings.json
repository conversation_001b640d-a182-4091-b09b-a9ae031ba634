{"unknownError": "Error descon<PERSON>", "authenticationFailed": "No se pudieron crear las incrustaciones: Error de autenticación. Comprueba tu clave de API.", "failedWithStatus": "No se pudieron crear las incrustaciones después de {{attempts}} intentos: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "No se pudieron crear las incrustaciones después de {{attempts}} intentos: {{errorMessage}}", "failedMaxAttempts": "No se pudieron crear las incrustaciones después de {{attempts}} intentos", "textExceedsTokenLimit": "El texto en el índice {{index}} supera el límite máximo de tokens ({{itemTokens}} > {{maxTokens}}). Omitiendo.", "rateLimitRetry": "Límite de velocidad alcanzado, reintentando en {{delayMs}}ms (intento {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "No se pudo leer el cuerpo del error", "requestFailed": "La solicitud de la API de Ollama falló con estado {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Estructura de respuesta inválida de la API de Ollama: array \"embeddings\" no encontrado o no es un array.", "embeddingFailed": "Incrustación de Ollama falló: {{message}}", "serviceNotRunning": "El servicio Ollama no se está ejecutando en {{baseUrl}}", "serviceUnavailable": "El servicio Ollama no está disponible (estado: {{status}})", "modelNotFound": "No se encuentra el modelo Ollama: {{modelId}}", "modelNotEmbeddingCapable": "El modelo Ollama no es capaz de realizar incrustaciones: {{modelId}}", "hostNotFound": "No se encuentra el host de Ollama: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Error desconocido procesando archivo {{filePath}}", "unknownErrorDeletingPoints": "Error desconocido eliminando puntos para {{filePath}}", "failedToProcessBatchWithError": "Error al procesar lote después de {{maxRetries}} intentos: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Error al conectar con la base de datos vectorial Qdrant. Asegúrate de que Qdrant esté funcionando y sea accesible en {{qdrantUrl}}. Error: {{errorMessage}}", "vectorDimensionMismatch": "No se pudo actualizar el índice de vectores para el nuevo modelo. Intenta borrar el índice y empezar de nuevo. Detalles: {{errorMessage}}"}, "validation": {"authenticationFailed": "Error de autenticación. Comprueba tu clave de API en los ajustes.", "connectionFailed": "Error al conectar con el servicio de embedder. Comprueba los ajustes de conexión y asegúrate de que el servicio esté funcionando.", "modelNotAvailable": "El modelo especificado no está disponible. Comprueba la configuración de tu modelo.", "configurationError": "Configuración de embedder no válida. Revisa tus ajustes.", "serviceUnavailable": "El servicio de embedder no está disponible. Asegúrate de que esté funcionando y sea accesible.", "invalidEndpoint": "Punto de conexión de API no válido. Comprueba la configuración de tu URL.", "invalidEmbedderConfig": "Configuración de embedder no válida. Comprueba tus ajustes.", "invalidApiKey": "Clave de API no válida. Comprueba la configuración de tu clave de API.", "invalidBaseUrl": "URL base no válida. Comprueba la configuración de tu URL.", "invalidModel": "Modelo no válido. Comprueba la configuración de tu modelo.", "invalidResponse": "Respuesta no válida del servicio de embedder. Comprueba tu configuración.", "apiKeyRequired": "Se requiere una clave de API para este embedder", "baseUrlRequired": "Se requiere una URL base para este embedder"}, "serviceFactory": {"openAiConfigMissing": "Falta la configuración de OpenAI para crear el incrustador", "ollamaConfigMissing": "Falta la configuración de Ollama para crear el incrustador", "openAiCompatibleConfigMissing": "Falta la configuración compatible con OpenAI para crear el incrustador", "geminiConfigMissing": "Falta la configuración de Gemini para crear el incrustador", "mistralConfigMissing": "Falta la configuración de Mistral para la creación del incrustador", "invalidEmbedderType": "Tipo de incrustador configurado inválido: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "No se pudo determinar la dimensión del vector para el modelo '{{modelId}}' con el proveedor '{{provider}}'. Asegúrate de que la 'Dimensión de incrustación' esté configurada correctamente en los ajustes del proveedor compatible con OpenAI.", "vectorDimensionNotDetermined": "No se pudo determinar la dimensión del vector para el modelo '{{modelId}}' con el proveedor '{{provider}}'. Verifica los perfiles del modelo o la configuración.", "qdrantUrlMissing": "Falta la URL de Qdrant para crear el almacén de vectores", "codeIndexingNotConfigured": "No se pueden crear servicios: La indexación de código no está configurada correctamente"}, "orchestrator": {"indexingFailedNoBlocks": "Indexación fallida: No se indexaron exitosamente bloques de código. Esto usualmente indica un problema de configuración del incrustador.", "indexingFailedCritical": "Indexación fallida: No se indexaron exitosamente bloques de código a pesar de encontrar archivos para procesar. Esto indica una falla crítica del incrustador.", "fileWatcherStarted": "Monitor de archivos iniciado.", "fileWatcherStopped": "Monitor de archivos detenido.", "failedDuringInitialScan": "Falló durante el escaneo inicial: {{errorMessage}}", "unknownError": "Error descon<PERSON>", "indexingRequiresWorkspace": "La indexación requiere una carpeta de workspace abierta"}}