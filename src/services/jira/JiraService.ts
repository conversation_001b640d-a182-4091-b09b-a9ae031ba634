import { Version2Client } from "jira.js"
import { JiraConfig, JiraIssue } from "@roo-code/types"

export interface JiraConnectionResult {
	success: boolean
	error?: string
	userInfo?: any
}

export interface JiraIssuesResult {
	success: boolean
	error?: string
	issues?: JiraIssue[]
}

export class JiraService {
	private static instance: JiraService
	private jiraClient: Version2Client | null = null
	private config: JiraConfig | null = null

	private constructor() {}

	static getInstance(): JiraService {
		if (!JiraService.instance) {
			JiraService.instance = new JiraService()
		}
		return JiraService.instance
	}

	async connect(config: JiraConfig): Promise<JiraConnectionResult> {
		try {
			// Validate config
			if (!config.url.trim()) {
				return { success: false, error: "请输入 JIRA 服务器地址" }
			}
			if (!config.username.trim()) {
				return { success: false, error: "请输入用户名" }
			}
			if (!config.password.trim()) {
				return { success: false, error: "请输入密码" }
			}

			// Create JIRA client
			this.jiraClient = new Version2Client({
				host: config.url,
				authentication: {
					basic: {
						email: config.username,
						apiToken: config.password,
					},
				},
			})

			// Test connection by getting current user info
			const userInfo = await this.jiraClient.myself.getCurrentUser()

			// Store config for later use
			this.config = config

			return {
				success: true,
				userInfo,
			}
		} catch (error: any) {
			console.error("JIRA 连接失败:", error)
			this.jiraClient = null
			this.config = null
			return {
				success: false,
				error: error.message || "连接失败，请检查配置信息",
			}
		}
	}

	async disconnect(): Promise<void> {
		this.jiraClient = null
		this.config = null
	}

	async getIssues(): Promise<JiraIssuesResult> {
		try {
			if (!this.jiraClient) {
				return { success: false, error: "未连接到 JIRA" }
			}

			// 首先获取当前用户信息用于调试
			let currentUser
			try {
				currentUser = await this.jiraClient.myself.getCurrentUser()
				console.log("当前 JIRA 用户信息:", {
					accountId: currentUser.accountId,
					emailAddress: currentUser.emailAddress,
					displayName: currentUser.displayName,
					username: currentUser.name,
				})
			} catch (error) {
				console.warn("无法获取当前用户信息:", error)
			}

			// 尝试多种 JQL 查询策略
			const jqlQueries = [
				`assignee = currentUser() ORDER BY updated DESC`,
				`assignee = "${this.config?.username}" ORDER BY updated DESC`,
				`assignee in (currentUser()) ORDER BY updated DESC`,
			]

			// 如果获取到了用户信息，添加更多查询选项
			if (currentUser) {
				if (currentUser.accountId) {
					jqlQueries.push(`assignee = "${currentUser.accountId}" ORDER BY updated DESC`)
				}
				if (currentUser.emailAddress) {
					jqlQueries.push(`assignee = "${currentUser.emailAddress}" ORDER BY updated DESC`)
				}
				if (currentUser.name) {
					jqlQueries.push(`assignee = "${currentUser.name}" ORDER BY updated DESC`)
				}
			}

			let searchResult
			let lastError

			// 依次尝试不同的查询
			for (const jql of jqlQueries) {
				try {
					console.log("尝试 JQL 查询:", jql)
					searchResult = await this.jiraClient.issueSearch.searchForIssuesUsingJql({
						jql,
						maxResults: 10,
						fields: [
							"summary",
							"description",
							"status",
							"priority",
							"assignee",
							"reporter",
							"project",
							"issuetype",
							"created",
							"updated",
							"duedate",
							"labels",
							"components",
							"fixVersions",
						],
					})

					if (searchResult.issues && searchResult.issues.length > 0) {
						console.log(`查询成功，找到 ${searchResult.issues.length} 个工单`)
						break
					} else {
						console.log("查询成功但未找到工单")
					}
				} catch (error: any) {
					console.warn(`JQL 查询失败 "${jql}":`, error.message)
					lastError = error
					continue
				}
			}

			// 如果所有查询都失败或没有结果，尝试一个更宽泛的查询用于调试
			if (!searchResult || !searchResult.issues || searchResult.issues.length === 0) {
				try {
					console.log("尝试宽泛查询以调试...")
					const debugResult = await this.jiraClient.issueSearch.searchForIssuesUsingJql({
						jql: `project IS NOT EMPTY ORDER BY updated DESC`,
						maxResults: 5,
						fields: ["summary", "assignee", "project"],
					})

					console.log("调试查询结果:", {
						total: debugResult.total,
						issues: debugResult.issues?.map((issue) => ({
							key: issue.key,
							summary: issue.fields.summary,
							assignee: issue.fields.assignee
								? {
										displayName: issue.fields.assignee.displayName,
										accountId: issue.fields.assignee.accountId,
										emailAddress: issue.fields.assignee.emailAddress,
									}
								: null,
						})),
					})
				} catch (debugError) {
					console.warn("调试查询也失败:", debugError)
				}
			}

			// 如果仍然没有结果
			if (!searchResult || !searchResult.issues || searchResult.issues.length === 0) {
				return {
					success: true,
					issues: [],
				}
			}

			const issues: JiraIssue[] =
				searchResult.issues?.map((issue: any) => ({
					key: issue.key,
					id: issue.id,
					summary: issue.fields.summary || "",
					description: issue.fields.description || "",
					status: {
						name: issue.fields.status?.name || "",
						id: issue.fields.status?.id || "",
						category: issue.fields.status?.statusCategory?.name || "",
					},
					priority: {
						name: issue.fields.priority?.name || "",
						id: issue.fields.priority?.id || "",
					},
					assignee: issue.fields.assignee
						? {
								displayName: issue.fields.assignee.displayName || "",
								emailAddress: issue.fields.assignee.emailAddress || "",
								accountId: issue.fields.assignee.accountId || "",
							}
						: null,
					reporter: {
						displayName: issue.fields.reporter?.displayName || "",
						emailAddress: issue.fields.reporter?.emailAddress || "",
						accountId: issue.fields.reporter?.accountId || "",
					},
					project: {
						key: issue.fields.project?.key || "",
						name: issue.fields.project?.name || "",
					},
					issueType: {
						name: issue.fields.issuetype?.name || "",
						id: issue.fields.issuetype?.id || "",
					},
					created: issue.fields.created,
					updated: issue.fields.updated,
					dueDate: issue.fields.duedate,
					labels: issue.fields.labels || [],
					components:
						issue.fields.components?.map((c: any) => ({
							name: c.name,
							id: c.id,
						})) || [],
					fixVersions:
						issue.fields.fixVersions?.map((v: any) => ({
							name: v.name,
							id: v.id,
						})) || [],
				})) || []

			console.log(`最终返回 ${issues.length} 个工单`)
			return {
				success: true,
				issues,
			}
		} catch (error: any) {
			console.error("获取工单失败:", error)
			return {
				success: false,
				error: error.message || "获取工单失败",
			}
		}
	}

	isConnected(): boolean {
		return this.jiraClient !== null && this.config !== null
	}

	getConfig(): JiraConfig | null {
		return this.config
	}
}
