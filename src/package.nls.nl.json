{"extension.displayName": "Zhanlu Code (voorheen Roo Cline)", "extension.description": "<PERSON><PERSON> compleet ontwi<PERSON>kel<PERSON><PERSON> van AI-agents in je editor.", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.activitybar.title": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplaats", "command.roomoteAgent.title": "Remote Agent", "command.openInEditor.title": "<PERSON>en in Editor", "command.settings.title": "Instellingen", "command.documentation.title": "Documentatie", "command.openInNewTab.title": "Openen in Nieuw Tabblad", "command.explainCode.title": "Leg Code Uit", "command.fixCode.title": "Repareer Code", "command.improveCode.title": "Verbeter Code", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "command.focusInput.title": "Focus op Invoerveld", "command.setCustomStoragePath.title": "Aangepast Opslagpad Instellen", "command.importSettings.title": "Instellingen Importeren", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> aan <PERSON>n", "command.terminal.fixCommand.title": "Repareer Dit Commando", "command.terminal.explainCommand.title": "Leg Dit Commando Uit", "command.acceptInput.title": "Invoer/Suggestie Accepteren", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "Commando's die automatisch kunnen worden uitgevoerd wanneer 'Altijd goedkeuren uitvoerbewerkingen' is ingeschakeld", "commands.deniedCommands.description": "Commando-prefixen die automatisch worden geweigerd zonder om goedkeuring te vragen. <PERSON><PERSON><PERSON> <PERSON><PERSON> met toegestane commando's heeft de langste prefix-match voorrang. Voeg * toe om alle commando's te weigeren.", "commands.commandExecutionTimeout.description": "Maximale tijd in seconden om te wachten tot commando-uitvoering voltooid is voordat er een timeout optreedt (0 = geen timeout, 1-600s, standaard: 0s)", "commands.commandTimeoutAllowlist.description": "Commando-prefixen die zijn uitgesloten van de commando-uitvoering timeout. Commando's die overeenko<PERSON> met deze prefixen worden uitgevoerd zonder timeout-beperkingen.", "settings.vsCodeLmModelSelector.description": "Instellingen voor VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "De leverancier van het taalmodel (bijv. copilot)", "settings.vsCodeLmModelSelector.family.description": "De familie van het taalmodel (bijv. gpt-4)", "settings.developerMode.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in om geavanceerde instellingen en functies weer te geven", "settings.customStoragePath.description": "Aangepast opslagpad. Laat leeg om de standaardlocatie te gebruiken. Ondersteunt absolute paden (bijv. 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Snelle correcties van Zhanlu Code inschakelen.", "settings.autoImportSettingsPath.description": "Pad naar een <PERSON>hanlu-configuratiebestand om automatisch te importeren bij het opstarten van de extensie. Ondersteunt absolute paden en paden ten opzichte van de thuismap (bijv. '~/Documents/zhanlu-settings.json'). Laat leeg om automatisch importeren uit te schakelen.", "settings.useAgentRules.description": "Laden van AGENTS.md-bestanden voor agentspecifieke regels inschakelen (zie https://agent-rules.org/)"}