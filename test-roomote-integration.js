#!/usr/bin/env node

/**
 * 测试 Roomote API 集成功能
 * 验证 VSCode 扩展是否能正确调用 Roomote API
 */

const BASE_URL = "http://localhost:3001"

// 测试 API 连通性
async function testApiConnectivity() {
	console.log("🔍 测试 Roomote API 连通性...")

	try {
		const response = await fetch(`${BASE_URL}/api/health`, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
			},
		})

		if (response.ok) {
			console.log("✅ Roomote API 服务运行正常")
			return true
		} else {
			console.log(`❌ API 健康检查失败: HTTP ${response.status}`)
			return false
		}
	} catch (error) {
		console.log(`❌ 无法连接到 Roomote API: ${error.message}`)
		console.log("   请确保 Roomote 服务正在运行:")
		console.log("   1. cd apps/roomote")
		console.log("   2. docker compose up api")
		return false
	}
}

// 测试自定义任务创建
async function testCustomTask() {
	console.log("\n🧪 测试自定义任务创建...")

	const payload = {
		type: "roomote.custom",
		payload: {
			customInstructions: "添加一个简单的工具函数，用于字符串格式化",
			gitlab: {
				repo: "test-user/demo-project",
				repoName: "demo-project",
				defaultBranch: "main",
				webUrl: "https://gitlab.example.com/test-user/demo-project",
				branch: "feature/test-integration",
			},
			mode: "code",
			apiProvider: "zhanlu",
			modelName: "zhanluAI",
		},
	}

	try {
		const response = await fetch(`${BASE_URL}/api/jobs`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(payload),
		})

		if (response.ok) {
			const result = await response.json()
			console.log("✅ 自定义任务创建成功")
			console.log(`   任务ID: ${result.jobId || result.enqueuedJobId}`)
			console.log(`   监控地址: http://localhost:3002/admin/queues`)
			return true
		} else {
			const errorText = await response.text()
			console.log(`❌ 任务创建失败: HTTP ${response.status}`)
			console.log(`   错误详情: ${errorText}`)
			return false
		}
	} catch (error) {
		console.log(`❌ 请求失败: ${error.message}`)
		return false
	}
}

// 测试 JIRA+GitLab 集成任务
async function testJiraGitlabTask() {
	console.log("\n🧪 测试 JIRA+GitLab 集成任务...")

	const payload = {
		type: "gitlab.jira.fix",
		payload: {
			jira: {
				ticket: "TEST-123",
				summary: "测试工单",
				description: "这是一个测试工单描述",
				priority: "Medium",
				assignee: "test-user",
				status: "In Progress",
			},
			gitlab: {
				repo: "test-user/demo-project",
				repoName: "demo-project",
				defaultBranch: "main",
				webUrl: "https://gitlab.example.com/test-user/demo-project",
				branch: "fix/TEST-123",
			},
			customInstructions: "请确保修复方案向后兼容",
			mode: "code",
			apiProvider: "zhanlu",
			modelName: "zhanluAI",
		},
	}

	try {
		const response = await fetch(`${BASE_URL}/api/jobs`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(payload),
		})

		if (response.ok) {
			const result = await response.json()
			console.log("✅ JIRA+GitLab 任务创建成功")
			console.log(`   任务ID: ${result.jobId || result.enqueuedJobId}`)
			console.log(`   监控地址: http://localhost:3002/admin/queues`)
			return true
		} else {
			const errorText = await response.text()
			console.log(`❌ 任务创建失败: HTTP ${response.status}`)
			console.log(`   错误详情: ${errorText}`)
			return false
		}
	} catch (error) {
		console.log(`❌ 请求失败: ${error.message}`)
		return false
	}
}

// 主测试函数
async function main() {
	console.log("========================================")
	console.log("🚀 Roomote API 集成测试")
	console.log("========================================")

	const apiConnected = await testApiConnectivity()

	if (!apiConnected) {
		console.log("\n❌ API 连接失败，跳过其他测试")
		process.exit(1)
	}

	const customTaskResult = await testCustomTask()
	const jiraTaskResult = await testJiraGitlabTask()

	console.log("\n========================================")
	console.log("📊 测试结果汇总")
	console.log("========================================")
	console.log(`API 连通性: ${apiConnected ? "✅ 通过" : "❌ 失败"}`)
	console.log(`自定义任务: ${customTaskResult ? "✅ 通过" : "❌ 失败"}`)
	console.log(`JIRA集成任务: ${jiraTaskResult ? "✅ 通过" : "❌ 失败"}`)

	if (customTaskResult && jiraTaskResult) {
		console.log("\n🎉 所有测试通过！VSCode 扩展现在可以正确创建 Roomote 任务。")
		console.log("\n💡 使用说明:")
		console.log("   1. 在 VSCode 中打开 Roomote Agent 面板")
		console.log("   2. 配置 GitLab 连接（必需）")
		console.log("   3. 可选择配置 JIRA 连接")
		console.log("   4. 输入自定义指令或选择 JIRA 工单")
		console.log("   5. 点击'创建任务'按钮")
		console.log("   6. 在监控面板查看任务执行状态")
	} else {
		console.log("\n⚠️  部分测试失败，请检查 Roomote 服务配置")
		process.exit(1)
	}
}

// 运行测试
main().catch((error) => {
	console.error("测试执行失败:", error)
	process.exit(1)
})
