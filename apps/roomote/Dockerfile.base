# docker compose build base

FROM node:20-slim AS base

# Install pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Set timezone to Asia/Shanghai (CST)
ENV TZ=Asia/Shanghai
RUN apt update && \
  apt install -y tzdata && \
  ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
  echo $TZ > /etc/timezone

# Install common system packages
RUN apt update && \
  apt install -y \
  curl \
  git \
  vim \
  jq \
  netcat-openbsd \
  apt-transport-https \
  ca-certificates \
  gnupg \
  lsb-release \
  wget \
  gpg \
  gh \
  && rm -rf /var/lib/apt/lists/*

# Install GitLab CLI (glab) - use fixed version
RUN curl -fsSL "https://gitlab.com/gitlab-org/cli/-/releases/v1.43.0/downloads/glab_1.43.0_Linux_x86_64.tar.gz" \
     -o glab.tar.gz \
  && tar xzf glab.tar.gz bin/glab --strip-components=1 \
  && chmod +x glab \
  && mv glab /usr/local/bin/ \
  && rm glab.tar.gz

# Install Docker cli
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
  && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
  && apt update && apt install -y docker-ce-cli \
  && rm -rf /var/lib/apt/lists/*
