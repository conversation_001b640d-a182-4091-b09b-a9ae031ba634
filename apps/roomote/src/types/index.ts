import { z } from "zod"

export interface JobTypes {
	"github.issue.fix": {
		repo: string
		issue: number
		title: string
		body: string
		labels?: string[]
		apiProvider?: string
		modelName?: string
		branch?: string
		profileConfiguration?: string // JSON 字符串格式的完整 profile 配置
	}
	"gitlab.jira.fix": {
		jira: {
			ticket: string
			summary: string
			description: string
			priority: string
			assignee: string
			status: string
		}
		gitlab: {
			repo: string
			repoName: string
			defaultBranch: string
			webUrl: string
			branch?: string
			config?: {
				url: string
				token: string
			}
		}
		customInstructions?: string
		mode?: string
		apiProvider?: string
		modelName?: string
		profileConfiguration?: string // JSON 字符串格式的完整 profile 配置
	}
	"roomote.custom": {
		customInstructions: string
		gitlab: {
			repo: string
			repoName: string
			defaultBranch: string
			webUrl: string
			branch: string
		}
		mode?: string
		apiProvider?: string
		modelName?: string
		profileConfiguration?: string // JSON 字符串格式的完整 profile 配置
	}
}

export type JobType = keyof JobTypes

export type JobStatus = "pending" | "processing" | "completed" | "failed"

export type JobPayload<T extends JobType = JobType> = JobTypes[T]

export type JobParams<T extends JobType> = {
	jobId: number
	type: T
	payload: JobPayload<T>
}

/**
 * CreateJob
 */

export const createJobSchema = z.discriminatedUnion("type", [
	z.object({
		type: z.literal("github.issue.fix"),
		payload: z.object({
			repo: z.string(),
			issue: z.number(),
			title: z.string(),
			body: z.string(),
			labels: z.array(z.string()).optional(),
			apiProvider: z.string().optional(),
			modelName: z.string().optional(),
			branch: z.string().optional(),
			profileConfiguration: z.string().optional(),
		}),
	}),
	z.object({
		type: z.literal("gitlab.jira.fix"),
		payload: z.object({
			jira: z.object({
				ticket: z.string(),
				summary: z.string(),
				description: z.string(),
				priority: z.string(),
				assignee: z.string(),
				status: z.string(),
			}),
			gitlab: z.object({
				repo: z.string(),
				repoName: z.string(),
				defaultBranch: z.string(),
				webUrl: z.string(),
				branch: z.string().optional(),
				config: z
					.object({
						url: z.string(),
						token: z.string(),
					})
					.optional(),
			}),
			customInstructions: z.string().optional(),
			mode: z.string().optional(),
			apiProvider: z.string().optional(),
			modelName: z.string().optional(),
			profileConfiguration: z.string().optional(),
		}),
	}),
	z.object({
		type: z.literal("roomote.custom"),
		payload: z.object({
			customInstructions: z.string(),
			gitlab: z.object({
				repo: z.string(),
				repoName: z.string(),
				defaultBranch: z.string(),
				webUrl: z.string(),
				branch: z.string(),
				config: z
					.object({
						url: z.string(),
						token: z.string(),
					})
					.optional(),
			}),
			mode: z.string().optional(),
			apiProvider: z.string().optional(),
			modelName: z.string().optional(),
			profileConfiguration: z.string().optional(),
		}),
	}),
])

export type CreateJob = z.infer<typeof createJobSchema>

/**
 * GitHubWebhook
 */

export const githubIssueWebhookSchema = z.object({
	action: z.string(),
	issue: z.object({
		number: z.number(),
		title: z.string(),
		body: z.string().nullable(),
		labels: z.array(z.object({ name: z.string() })),
	}),
	repository: z.object({
		full_name: z.string(),
	}),
})

export type GitHubIssueWebhook = z.infer<typeof githubIssueWebhookSchema>

export const githubPullRequestWebhookSchema = z.object({
	action: z.string(),
	pull_request: z.object({
		number: z.number(),
		title: z.string(),
		body: z.string().nullable(),
		html_url: z.string(),
	}),
	repository: z.object({
		full_name: z.string(),
	}),
})

export type GitHubPullRequestWebhook = z.infer<typeof githubPullRequestWebhookSchema>
