import { NextRequest, NextResponse } from "next/server"
import { eq } from "drizzle-orm"

import { db, cloudJobs } from "@/db"

// 禁用缓存，确保总是返回最新数据
export const dynamic = "force-dynamic"
export const revalidate = 0

type Params = Promise<{ id: string }>

export async function GET(request: NextRequest, { params }: { params: Params }) {
	try {
		const { id } = await params
		const jobId = parseInt(id, 10)

		if (isNaN(jobId)) {
			return NextResponse.json({ error: "Invalid job ID" }, { status: 400 })
		}

		const [job] = await db.select().from(cloudJobs).where(eq(cloudJobs.id, jobId)).limit(1)

		if (!job) {
			return NextResponse.json({ error: "Job not found" }, { status: 404 })
		}

		// 添加缓存控制头
		const response = NextResponse.json({
			id: job.id,
			type: job.type,
			status: job.status,
			payload: job.payload,
			result: job.result,
			error: job.error,
			taskId: job.taskId,
			createdAt: job.createdAt,
			startedAt: job.startedAt,
			completedAt: job.completedAt,
		})

		// 禁用客户端和代理缓存
		response.headers.set("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate")
		response.headers.set("Pragma", "no-cache")
		response.headers.set("Expires", "0")

		return response
	} catch (error) {
		console.error("Error fetching job:", error)
		return NextResponse.json({ error: "Internal server error" }, { status: 500 })
	}
}
