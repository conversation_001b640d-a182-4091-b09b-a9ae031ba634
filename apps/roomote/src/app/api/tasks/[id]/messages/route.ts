import { NextRequest, NextResponse } from "next/server"
import { redis } from "@/lib/redis"
import fs from "fs/promises"
import path from "path"

import { db, cloudJobs } from "@/db"

// 禁用缓存，确保总是返回最新数据
export const dynamic = "force-dynamic"
export const revalidate = 0

type Params = Promise<{ id: string }>

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: taskId } = await params
	const { searchParams } = new URL(request.url)
	const stream = searchParams.get("stream") === "true"

	if (stream) {
		// 服务器发送事件(SSE)实现实时推送
		const encoder = new TextEncoder()

		const readable = new ReadableStream({
			start(controller) {
				console.log(`[MessageAPI] Starting SSE stream for task ${taskId}`)

				// 创建 Redis 订阅客户端
				const subscriber = redis.duplicate()
				let isAlive = true

				const cleanup = () => {
					if (!isAlive) return
					isAlive = false
					subscriber.disconnect()
					controller.close()
				}

				// 订阅任务消息频道
				subscriber.subscribe(`task:${taskId}:messages`)

				subscriber.on("message", (channel, message) => {
					if (!isAlive) return

					try {
						console.log(`[MessageAPI] Broadcasting message for task ${taskId}`)
						const data = `data: ${message}\n\n`
						controller.enqueue(encoder.encode(data))
					} catch (error) {
						console.error(`[MessageAPI] Error broadcasting message for task ${taskId}:`, error)
					}
				})

				subscriber.on("error", (error) => {
					console.error(`[MessageAPI] Redis subscription error for task ${taskId}:`, error)
					cleanup()
				})

				// 发送当前消息作为初始状态
				getTaskMessages(taskId)
					.then((messages) => {
						if (!isAlive) return

						try {
							const data = `data: ${JSON.stringify(messages)}\n\n`
							controller.enqueue(encoder.encode(data))
						} catch (error) {
							console.error(`[MessageAPI] Error sending initial messages for task ${taskId}:`, error)
						}
					})
					.catch((error) => {
						console.error(`[MessageAPI] Error loading initial messages for task ${taskId}:`, error)
					})

				// 发送心跳保持连接
				const heartbeat = setInterval(() => {
					if (!isAlive) {
						clearInterval(heartbeat)
						return
					}

					try {
						controller.enqueue(encoder.encode(": heartbeat\n\n"))
					} catch (error) {
						console.error(`[MessageAPI] Heartbeat error for task ${taskId}:`, error)
						cleanup()
					}
				}, 30000) // 30秒心跳

				// 清理函数
				return () => {
					clearInterval(heartbeat)
					cleanup()
				}
			},
		})

		return new Response(readable, {
			headers: {
				"Content-Type": "text/event-stream",
				"Cache-Control": "no-cache",
				Connection: "keep-alive",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Headers": "Cache-Control",
			},
		})
	} else {
		// 普通REST API返回当前消息
		try {
			const messages = await getTaskMessages(taskId)
			const response = NextResponse.json({ messages, success: true })

			// 禁用客户端和代理缓存
			response.headers.set("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate")
			response.headers.set("Pragma", "no-cache")
			response.headers.set("Expires", "0")

			return response
		} catch (error) {
			console.error(`[MessageAPI] Error getting messages for task ${taskId}:`, error)
			return NextResponse.json({ error: "Failed to get messages", success: false }, { status: 500 })
		}
	}
}

async function getTaskMessages(jobId: string) {
	// 从数据库获取真实的task ID
	let realTaskId: string | null = null

	try {
		const { db, cloudJobs } = await import("@/db")
		const { eq } = await import("drizzle-orm")

		const job = await db
			.select()
			.from(cloudJobs)
			.where(eq(cloudJobs.id, parseInt(jobId)))
			.limit(1)

		if (job.length === 0) {
			console.log(`[MessageAPI] Job ${jobId} not found`)
			return []
		}

		realTaskId = job[0]?.taskId || null
		console.log(`[MessageAPI] Job ${jobId} taskId from DB: ${realTaskId}`)

		if (!realTaskId) {
			console.log(`[MessageAPI] Job ${jobId} has no taskId, returning empty messages`)
			return []
		}

		console.log(`[MessageAPI] Job ${jobId} maps to task ${realTaskId}`)
	} catch (error) {
		console.error(`[MessageAPI] Error getting messages for job ${jobId}:`, error)
		return []
	}

	if (!realTaskId) {
		return []
	}

	// 使用真实的task ID查找消息文件
	const sharedPath = path.join("/roo/shared/messages", `${realTaskId}.json`)

	try {
		const content = await fs.readFile(sharedPath, "utf8")
		const messages = JSON.parse(content)
		console.log(
			`[MessageAPI] Loaded ${messages.length} messages from shared path for task ${realTaskId} (job ${jobId})`,
		)
		return messages
	} catch (error) {
		// 如果共享目录文件不存在，尝试从 globalStorage 路径读取
		const globalStoragePath = `/roo/.vscode/User/globalStorage/ecloud.zhanlu/tasks/${realTaskId}/ui_messages.json`

		try {
			const content = await fs.readFile(globalStoragePath, "utf8")
			const messages = JSON.parse(content)
			console.log(
				`[MessageAPI] Loaded ${messages.length} messages from globalStorage for task ${realTaskId} (job ${jobId})`,
			)
			return messages
		} catch (globalError) {
			console.log(`[MessageAPI] No messages found for task ${realTaskId} (job ${jobId}), returning empty array`)
			return []
		}
	}
}
