import fs from "fs"
import path from "path"
import { redis } from "./redis"

export class MessageBridge {
	private watchedTasks = new Map<string, fs.FSWatcher>()

	constructor() {
		console.log("[MessageBridge] Initialized")
	}

	async startWatching(taskId: string): Promise<void> {
		console.log(`[MessageBridge] Starting to watch task ${taskId}`)

		const messagesPath = `/roo/.vscode/User/globalStorage/ecloud.zhanlu/tasks/${taskId}/ui_messages.json`
		const sharedPath = `/roo/shared/messages/${taskId}.json`

		// 确保共享目录存在
		const sharedDir = path.dirname(sharedPath)
		try {
			await fs.promises.mkdir(sharedDir, { recursive: true })
		} catch (error) {
			console.error(`[MessageBridge] Error creating shared directory:`, error)
		}

		// 如果消息文件已存在，立即同步一次
		try {
			if (fs.existsSync(messagesPath)) {
				await this.syncMessages(taskId, messagesPath, sharedPath)
			}
		} catch (error) {
			console.error(`[MessageBridge] Error in initial sync for task ${taskId}:`, error)
		}

		// 监控文件变化
		try {
			const watcher = fs.watch(messagesPath, { persistent: true }, async (eventType, filename) => {
				if (eventType === "change") {
					console.log(`[MessageBridge] File change detected for task ${taskId}`)
					try {
						await this.syncMessages(taskId, messagesPath, sharedPath)
					} catch (error) {
						console.error(`[MessageBridge] Error syncing messages for task ${taskId}:`, error)
					}
				}
			})

			watcher.on("error", (error) => {
				console.error(`[MessageBridge] Watcher error for task ${taskId}:`, error)
				this.stopWatching(taskId)
			})

			this.watchedTasks.set(taskId, watcher)
			console.log(`[MessageBridge] Started watching task ${taskId} at ${messagesPath}`)
		} catch (error) {
			console.error(`[MessageBridge] Error setting up watcher for task ${taskId}:`, error)
		}
	}

	private async syncMessages(taskId: string, sourcePath: string, sharedPath: string): Promise<void> {
		try {
			// 读取源文件
			const messages = await fs.promises.readFile(sourcePath, "utf8")

			// 验证 JSON 格式
			let parsedMessages
			try {
				parsedMessages = JSON.parse(messages)
			} catch (parseError) {
				console.error(`[MessageBridge] Invalid JSON in messages file for task ${taskId}:`, parseError)
				return
			}

			// 复制到共享目录
			await fs.promises.writeFile(sharedPath, messages, "utf8")

			// 推送到Redis以供API使用
			await redis.publish(`task:${taskId}:messages`, messages)

			console.log(`[MessageBridge] Synced ${parsedMessages.length} messages for task ${taskId}`)
		} catch (error: any) {
			if (error.code === "ENOENT") {
				console.log(`[MessageBridge] Messages file not found for task ${taskId}, skipping sync`)
			} else {
				console.error(`[MessageBridge] Error syncing messages for task ${taskId}:`, error)
			}
		}
	}

	async stopWatching(taskId: string): Promise<void> {
		const watcher = this.watchedTasks.get(taskId)
		if (watcher) {
			watcher.close()
			this.watchedTasks.delete(taskId)
			console.log(`[MessageBridge] Stopped watching task ${taskId}`)
		}
	}

	async stopAll(): Promise<void> {
		console.log(`[MessageBridge] Stopping all watchers (${this.watchedTasks.size} tasks)`)
		for (const [taskId, watcher] of this.watchedTasks) {
			watcher.close()
			console.log(`[MessageBridge] Stopped watching task ${taskId}`)
		}
		this.watchedTasks.clear()
	}

	getWatchedTasks(): string[] {
		return Array.from(this.watchedTasks.keys())
	}
}
