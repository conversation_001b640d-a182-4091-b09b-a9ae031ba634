import * as path from "path"
import * as os from "node:os"
import * as fs from "node:fs/promises"
import { execSync } from "node:child_process"
import crypto from "node:crypto"

import { execa } from "execa"
import pWaitFor from "p-wait-for"

import { type TaskEvent, RooCodeEventName, EVALS_SETTINGS, IpcMessageType, TaskCommandName } from "@roo-code/types"
import { IpcClient } from "@roo-code/ipc"

import type { JobPayload, JobType } from "@/types"

import { Logger } from "./logger"
import { isDockerContainer } from "./utils"
import { SlackNotifier } from "./slack"
import { MessageBridge } from "./messageBridge"

const TIMEOUT = 30 * 60 * 1_000

class SubprocessTimeoutError extends Error {
	constructor(timeout: number) {
		super(`Subprocess timeout after ${timeout}ms`)
		this.name = "SubprocessTimeoutError"
	}
}

export type RunTaskCallbacks = {
	onTaskStarted?: (slackThreadTs: string | null, rooTaskId: string) => Promise<void>
	onTaskAborted?: (slackThreadTs: string | null) => Promise<void>
	onTaskCompleted?: (
		slackThreadTs: string | null,
		success: boolean,
		duration: number,
		rooTaskId?: string,
	) => Promise<void>
	onTaskTimedOut?: (slackThreadTs: string | null) => Promise<void>
	onClientDisconnected?: (slackThreadTs: string | null) => Promise<void>
}

type RunTaskOptions<T extends JobType> = {
	jobType: T
	jobPayload: JobPayload<T>
	prompt: string
	publish: (taskEvent: TaskEvent) => Promise<void>
	logger: Logger
	callbacks?: RunTaskCallbacks
	configuration?: {
		apiProvider?: string
		modelName?: string
	}
}

// 仓库信息接口
interface RepoInfo {
	owner: string
	name: string
	fullName: string
	branch?: string
	repoType: "github" | "gitlab"
}

// 从jobPayload中提取仓库信息
function getRepoInfoFromPayload<T extends JobType>(jobPayload: JobPayload<T>): RepoInfo {
	let repoFullName: string
	let branch: string = "main" // 默认分支
	let repoType: "github" | "gitlab" = "github" // 默认类型

	// 根据不同的jobType提取repo信息
	if ("repo" in jobPayload && typeof jobPayload.repo === "string") {
		// GitHub任务类型：直接从repo字段获取
		repoFullName = jobPayload.repo
		repoType = "github"

		// 提取GitHub分支信息
		if ("branch" in jobPayload && typeof jobPayload.branch === "string" && jobPayload.branch.trim() !== "") {
			branch = jobPayload.branch.trim()
		}
	} else if ("gitlab" in jobPayload && typeof jobPayload.gitlab === "object" && jobPayload.gitlab !== null) {
		// GitLab相关任务类型：从gitlab字段获取
		const gitlabPayload = jobPayload.gitlab as any
		if ("repo" in gitlabPayload && typeof gitlabPayload.repo === "string") {
			repoFullName = gitlabPayload.repo
			repoType = "gitlab"

			// 提取GitLab分支信息 - 优先使用指定的branch，否则使用defaultBranch
			if (
				"branch" in gitlabPayload &&
				typeof gitlabPayload.branch === "string" &&
				gitlabPayload.branch.trim() !== ""
			) {
				branch = gitlabPayload.branch.trim()
			} else if (
				"defaultBranch" in gitlabPayload &&
				typeof gitlabPayload.defaultBranch === "string" &&
				gitlabPayload.defaultBranch.trim() !== ""
			) {
				branch = gitlabPayload.defaultBranch.trim()
			}
		} else {
			throw new Error("Invalid GitLab payload: missing or invalid repo field")
		}
	} else {
		// 默认仓库 - 只在没有任何仓库信息时使用
		repoFullName = "village-way/Roo-Code"
		repoType = "github"
	}

	const [owner, name] = repoFullName.split("/")
	if (!owner || !name) {
		throw new Error(`Invalid repository format: ${repoFullName}. Expected format: owner/repo`)
	}

	return {
		owner,
		name,
		fullName: repoFullName,
		branch, // 使用指定的分支或默认分支
		repoType,
	}
}

// 设置工作空间
async function setupWorkspace(repoInfo: RepoInfo, logger: Logger): Promise<string> {
	const baseDir = "/roo/repos"

	// 为GitLab仓库创建子目录结构
	// 例如：paas-aipt/ai/zhanlu-vs -> ai/zhanlu-vs
	let workspacePath: string
	if (repoInfo.repoType === "gitlab") {
		const pathParts = repoInfo.fullName.split("/")
		if (pathParts.length > 2) {
			// 对于多级路径，使用从第二部分开始的路径
			const subPath = pathParts.slice(1).join("/")
			workspacePath = path.join(baseDir, subPath)
		} else {
			// 对于两级路径，直接使用完整路径
			workspacePath = path.join(baseDir, repoInfo.fullName)
		}
	} else {
		// GitHub仓库保持原有逻辑
		workspacePath = path.join(baseDir, repoInfo.name)
	}

	try {
		// 确保基础目录和父目录存在
		await fs.mkdir(path.dirname(workspacePath), { recursive: true })

		// 检查工作目录是否已存在
		const workspaceExists = await fs
			.access(workspacePath)
			.then(() => true)
			.catch(() => false)

		if (workspaceExists) {
			logger.info(`[setupWorkspace] Workspace already exists: ${workspacePath}`)

			// 检查是否是正确的仓库
			const isCorrectRepo = await verifyRepository(workspacePath, repoInfo, logger)

			if (isCorrectRepo) {
				// 更新代码库
				await updateRepository(workspacePath, repoInfo, logger)
				return workspacePath
			} else {
				// 删除错误的仓库并重新克隆
				logger.info(`[setupWorkspace] Removing incorrect repository at ${workspacePath}`)
				await fs.rm(workspacePath, { recursive: true, force: true })
			}
		}

		// 克隆仓库
		await cloneRepository(repoInfo, workspacePath, logger)
		return workspacePath
	} catch (error) {
		logger.error(`[setupWorkspace] Failed to setup workspace: ${error}`)
		throw error
	}
}

// 验证仓库是否正确
async function verifyRepository(workspacePath: string, repoInfo: RepoInfo, logger: Logger): Promise<boolean> {
	try {
		const gitDir = path.join(workspacePath, ".git")
		const gitExists = await fs
			.access(gitDir)
			.then(() => true)
			.catch(() => false)

		if (!gitExists) {
			return false
		}

		// 检查远程仓库URL
		const remoteUrl = execSync("git remote get-url origin", {
			cwd: workspacePath,
			encoding: "utf8",
		}).trim()

		// 支持GitHub和GitLab的HTTPS和SSH格式的URL
		const gitlabUrl = process.env.GITLAB_URL || "http://gitlab.cmss.com"
		const expectedUrls = [
			// GitHub URLs
			`https://github.com/${repoInfo.fullName}.git`,
			`https://github.com/${repoInfo.fullName}`,
			`**************:${repoInfo.fullName}.git`,
			// GitLab URLs
			`${gitlabUrl}/${repoInfo.fullName}.git`,
			`${gitlabUrl}/${repoInfo.fullName}`,
			`git@${gitlabUrl.replace(/^https?:\/\//, "")}:${repoInfo.fullName}.git`,
		]

		const isCorrect = expectedUrls.some((url) => remoteUrl === url)

		if (!isCorrect) {
			logger.info(`[verifyRepository] Repository mismatch. Expected: ${repoInfo.fullName}, Found: ${remoteUrl}`)
		}

		return isCorrect
	} catch (error) {
		logger.error(`[verifyRepository] Error verifying repository: ${error}`)
		return false
	}
}

// 克隆仓库
async function cloneRepository(repoInfo: RepoInfo, workspacePath: string, logger: Logger): Promise<void> {
	const branchInfo = repoInfo.branch ? ` (branch: ${repoInfo.branch})` : " (default branch)"
	logger.info(`[cloneRepository] Cloning ${repoInfo.fullName}${branchInfo} to ${workspacePath}`)

	try {
		let cloneCmd: string

		// 根据仓库类型选择克隆方式
		if (repoInfo.repoType === "gitlab") {
			// GitLab仓库克隆
			const gitlabUrl = process.env.GITLAB_URL || "http://gitlab.cmss.com"
			const gitlabPat = process.env.GITLAB_PAT

			if (!gitlabPat) {
				throw new Error("GITLAB_PAT environment variable is required for GitLab repository access")
			}

			// 使用与原始URL相同的协议 + token认证方式克隆GitLab仓库
			// 构建正确的认证URL：protocol://oauth2:<EMAIL>/group/project.git
			const protocol = gitlabUrl.startsWith("https://") ? "https" : "http"
			const baseUrl = gitlabUrl.replace(/^https?:\/\//, "") // 移除协议前缀
			const fullRepoUrl = `${protocol}://oauth2:${gitlabPat}@${baseUrl}/${repoInfo.fullName}.git`
			cloneCmd = `git clone ${fullRepoUrl} ${workspacePath}`

			logger.info(`[cloneRepository] Using GitLab authentication for ${gitlabUrl}/${repoInfo.fullName}`)
		} else {
			// GitHub仓库克隆 - 使用GitHub CLI
			cloneCmd = `gh repo clone ${repoInfo.fullName} ${workspacePath}`
		}

		// 执行克隆命令
		execSync(cloneCmd, {
			stdio: "inherit",
			encoding: "utf8",
		})

		// 如果指定了分支且不是默认分支，切换到指定分支
		if (repoInfo.branch && repoInfo.branch !== "main" && repoInfo.branch !== "master") {
			try {
				// 尝试切换到指定分支（可能是远程分支）
				execSync(`git checkout -b ${repoInfo.branch} origin/${repoInfo.branch}`, {
					cwd: workspacePath,
					stdio: "inherit",
				})
				logger.info(`[cloneRepository] Switched to branch: ${repoInfo.branch}`)
			} catch (branchError) {
				// 如果远程分支不存在，尝试本地分支
				try {
					execSync(`git checkout ${repoInfo.branch}`, {
						cwd: workspacePath,
						stdio: "inherit",
					})
					logger.info(`[cloneRepository] Switched to existing branch: ${repoInfo.branch}`)
				} catch (localBranchError) {
					logger.warn(
						`[cloneRepository] Could not switch to branch ${repoInfo.branch}, staying on default branch`,
					)
				}
			}
		}

		// 只为GitHub仓库设置默认仓库
		if (repoInfo.repoType === "github") {
			try {
				execSync(`gh repo set-default ${repoInfo.fullName}`, {
					cwd: workspacePath,
					stdio: "inherit",
				})
			} catch (error) {
				logger.warn(`[cloneRepository] Could not set default repository: ${error}`)
			}
		}

		// 安装依赖（如果存在package.json）
		await installDependencies(workspacePath, logger)

		logger.info(`[cloneRepository] Successfully cloned ${repoInfo.fullName}${branchInfo}`)
	} catch (error) {
		logger.error(`[cloneRepository] Failed to clone repository: ${error}`)
		throw error
	}
}

// 更新仓库
async function updateRepository(workspacePath: string, repoInfo: RepoInfo, logger: Logger): Promise<void> {
	const branchInfo = repoInfo.branch ? ` (branch: ${repoInfo.branch})` : ""
	logger.info(`[updateRepository] Updating repository at ${workspacePath}${branchInfo}`)

	try {
		// 拉取最新代码
		execSync("git fetch origin", {
			cwd: workspacePath,
			stdio: "inherit",
		})

		// 获取当前分支
		const currentBranch = execSync("git branch --show-current", {
			cwd: workspacePath,
			encoding: "utf8",
		}).trim()

		// 如果指定了分支，确保切换到正确的分支
		if (repoInfo.branch && currentBranch !== repoInfo.branch) {
			try {
				// 尝试切换到指定分支
				execSync(`git checkout ${repoInfo.branch}`, {
					cwd: workspacePath,
					stdio: "inherit",
				})
				logger.info(`[updateRepository] Switched to branch: ${repoInfo.branch}`)
			} catch (checkoutError) {
				// 如果本地分支不存在，尝试从远程创建
				try {
					execSync(`git checkout -b ${repoInfo.branch} origin/${repoInfo.branch}`, {
						cwd: workspacePath,
						stdio: "inherit",
					})
					logger.info(`[updateRepository] Created and switched to branch: ${repoInfo.branch}`)
				} catch (createError) {
					logger.warn(
						`[updateRepository] Could not switch to branch ${repoInfo.branch}, staying on ${currentBranch}`,
					)
				}
			}
		}

		// 更新当前分支
		const updateBranch = repoInfo.branch || currentBranch
		if (updateBranch) {
			execSync(`git pull origin ${updateBranch}`, {
				cwd: workspacePath,
				stdio: "inherit",
			})
		} else {
			// 如果无法确定分支，使用默认的pull
			execSync("git pull", {
				cwd: workspacePath,
				stdio: "inherit",
			})
		}

		// 更新依赖
		await installDependencies(workspacePath, logger)

		logger.info(`[updateRepository] Successfully updated repository${branchInfo}`)
	} catch (error) {
		logger.error(`[updateRepository] Failed to update repository: ${error}`)
		// 更新失败不是致命错误，继续执行
	}
}

// 安装依赖
async function installDependencies(workspacePath: string, logger: Logger): Promise<void> {
	try {
		// 检查环境变量控制依赖安装
		const skipInstall = process.env.SKIP_DEPENDENCY_INSTALL === "true"
		logger.info(
			`[installDependencies] SKIP_DEPENDENCY_INSTALL: ${skipInstall} ${process.env.SKIP_DEPENDENCY_INSTALL}`,
		)
		if (skipInstall) {
			logger.info(`[installDependencies] Skipping dependency installation (SKIP_DEPENDENCY_INSTALL=true)`)
			return
		}

		const packageJsonPath = path.join(workspacePath, "package.json")
		const packageJsonExists = await fs
			.access(packageJsonPath)
			.then(() => true)
			.catch(() => false)

		if (packageJsonExists) {
			logger.info(`[installDependencies] Installing dependencies in ${workspacePath}`)

			// 检查使用的包管理器
			const packageManager = await detectPackageManager(workspacePath)

			execSync(`${packageManager} install`, {
				cwd: workspacePath,
				stdio: "inherit",
			})

			logger.info(`[installDependencies] Dependencies installed successfully`)
		}
	} catch (error) {
		logger.error(`[installDependencies] Failed to install dependencies: ${error}`)
		// 依赖安装失败不是致命错误
	}
}

// 检测包管理器
async function detectPackageManager(workspacePath: string): Promise<string> {
	const files = await fs.readdir(workspacePath)

	if (files.includes("pnpm-lock.yaml")) {
		return "pnpm"
	} else if (files.includes("yarn.lock")) {
		return "yarn"
	} else {
		return "npm"
	}
}

// Profile 配置类型定义
interface ProfileConfig {
	apiProvider?: string
	zhanluModelId?: string
	openRouterModelId?: string
	apiModelId?: string
	openAiModelId?: string
	ollamaModelId?: string
	modelName?: string
	// DeepSeek 专用字段
	deepSeekApiKey?: string
	deepSeekBaseUrl?: string
	// OpenAI Compatible 字段
	openAiApiKey?: string
	openAiBaseUrl?: string
	// 新增的批准项配置
	alwaysAllowFollowupQuestions?: boolean
	followupAutoApproveTimeoutMs?: number
	alwaysAllowUpdateTodoList?: boolean
	// 命令执行相关配置
	commandExecutionTimeout?: number
	commandTimeoutAllowlist?: string[]
	deniedCommands?: string[]
	preventCompletionWithOpenTodos?: boolean
	[key: string]: any // 允许其他配置字段
}

// 简化的配置应用函数：直接使用插件导出的配置格式
function applyImportedConfiguration(settings: any): any {
	const { providerProfiles, globalSettings } = settings

	if (!providerProfiles?.currentApiConfigName || !providerProfiles?.apiConfigs) {
		return null
	}

	const currentProfileName = providerProfiles.currentApiConfigName
	const currentProfile = providerProfiles.apiConfigs[currentProfileName]

	if (!currentProfile) {
		return null
	}

	// 直接使用插件导出的配置，无需复杂的适配逻辑
	// 插件的导出功能已经确保了配置的完整性和正确性
	const configuration = {
		...currentProfile,
		...globalSettings,
		// 保护重要的环境变量
		openRouterApiKey: process.env.OPENROUTER_API_KEY || currentProfile.openRouterApiKey,
	}

	return {
		configuration,
		profileName: currentProfileName,
		provider: currentProfile.apiProvider || "unknown",
	}
}

// 从配置中提取模型名称 - 简化版本，直接使用插件导出的配置
function getModelFromConfig(profileConfig: any): string {
	if (!profileConfig || typeof profileConfig !== "object") {
		return "unknown"
	}

	// 优先使用provider特定的模型ID字段
	const modelFields = [
		"apiModelId", // 通用模型ID
		"openAiModelId", // OpenAI
		"zhanluModelId", // Zhanlu
		"openRouterModelId", // OpenRouter
		"ollamaModelId", // Ollama
		"glamaModelId", // Glama
		"unboundModelId", // Unbound
		"requestyModelId", // Requesty
		"litellmModelId", // LiteLLM
		"lmStudioModelId", // LM Studio
		"modelName", // 通用模型名称
	]

	// 按优先级查找模型名称
	for (const field of modelFields) {
		if (profileConfig[field]) {
			return profileConfig[field]
		}
	}

	// 如果都没有找到，返回默认值
	return "unknown"
}

// 设置模型字段到配置中 - 通用版本，基于provider类型
function setModelInConfig(config: any, modelName: string, apiProvider: string): void {
	// 根据provider类型设置相应的模型字段
	const providerModelFields: Record<string, string> = {
		zhanlu: "zhanluModelId",
		openrouter: "openRouterModelId",
		openai: "openAiModelId",
		ollama: "ollamaModelId",
		glama: "glamaModelId",
		unbound: "unboundModelId",
		requesty: "requestyModelId",
		litellm: "litellmModelId",
		lmstudio: "lmStudioModelId",
	}

	// 使用provider特定字段，如果没有则使用通用字段
	const modelField = providerModelFields[apiProvider] || "apiModelId"
	Object.assign(config, { [modelField]: modelName })
}

// 安全地合并配置，避免覆盖重要的环境变量和自动化执行权限
function mergeConfigurations(baseConfig: any, profileConfig: ProfileConfig): any {
	return {
		...baseConfig,
		...profileConfig,
		// 保护重要的环境变量配置
		openRouterApiKey:
			process.env.OPENROUTER_API_KEY || profileConfig.openRouterApiKey || baseConfig.openRouterApiKey,

		// 保护自动批准和权限配置，确保Remote Agent能自动化执行
		// 使用EVALS_SETTINGS中的配置值保持一致性
		autoApprovalEnabled: baseConfig.autoApprovalEnabled !== undefined ? baseConfig.autoApprovalEnabled : true,

		// 文件读写权限
		alwaysAllowReadOnly: baseConfig.alwaysAllowReadOnly !== undefined ? baseConfig.alwaysAllowReadOnly : true,
		alwaysAllowWrite: baseConfig.alwaysAllowWrite !== undefined ? baseConfig.alwaysAllowWrite : true,
		alwaysAllowWriteOutsideWorkspace:
			baseConfig.alwaysAllowWriteOutsideWorkspace !== undefined
				? baseConfig.alwaysAllowWriteOutsideWorkspace
				: false,
		alwaysAllowWriteProtected:
			baseConfig.alwaysAllowWriteProtected !== undefined ? baseConfig.alwaysAllowWriteProtected : false,

		// 命令执行权限
		alwaysAllowExecute: baseConfig.alwaysAllowExecute !== undefined ? baseConfig.alwaysAllowExecute : true,
		allowedCommands: baseConfig.allowedCommands || ["*"],
		deniedCommands: baseConfig.deniedCommands || [],
		commandExecutionTimeout:
			baseConfig.commandExecutionTimeout !== undefined ? baseConfig.commandExecutionTimeout : 20,
		commandTimeoutAllowlist: baseConfig.commandTimeoutAllowlist || [],
		preventCompletionWithOpenTodos:
			baseConfig.preventCompletionWithOpenTodos !== undefined ? baseConfig.preventCompletionWithOpenTodos : false,

		// 其他自动化权限
		alwaysAllowBrowser: baseConfig.alwaysAllowBrowser !== undefined ? baseConfig.alwaysAllowBrowser : true,
		alwaysAllowMcp: baseConfig.alwaysAllowMcp !== undefined ? baseConfig.alwaysAllowMcp : true,
		alwaysAllowModeSwitch: baseConfig.alwaysAllowModeSwitch !== undefined ? baseConfig.alwaysAllowModeSwitch : true,
		alwaysAllowSubtasks: baseConfig.alwaysAllowSubtasks !== undefined ? baseConfig.alwaysAllowSubtasks : true,
		alwaysApproveResubmit: baseConfig.alwaysApproveResubmit !== undefined ? baseConfig.alwaysApproveResubmit : true,

		// 新增的批准项配置 - 问题和待办
		alwaysAllowFollowupQuestions:
			baseConfig.alwaysAllowFollowupQuestions !== undefined ? baseConfig.alwaysAllowFollowupQuestions : true,
		followupAutoApproveTimeoutMs:
			baseConfig.followupAutoApproveTimeoutMs !== undefined ? baseConfig.followupAutoApproveTimeoutMs : 0,
		alwaysAllowUpdateTodoList:
			baseConfig.alwaysAllowUpdateTodoList !== undefined ? baseConfig.alwaysAllowUpdateTodoList : true,

		// 性能和限制配置
		writeDelayMs: baseConfig.writeDelayMs !== undefined ? baseConfig.writeDelayMs : 1000,
		requestDelaySeconds: baseConfig.requestDelaySeconds !== undefined ? baseConfig.requestDelaySeconds : 10,
		rateLimitSeconds: baseConfig.rateLimitSeconds !== undefined ? baseConfig.rateLimitSeconds : 0,
		maxReadFileLine: baseConfig.maxReadFileLine !== undefined ? baseConfig.maxReadFileLine : -1, // -1 启用完整文件读取

		// 终端配置 - 强制禁用VS Code shell integration
		terminalShellIntegrationDisabled: true,
	}
}

export const runTask = async <T extends JobType>({
	jobType,
	jobPayload,
	prompt,
	publish,
	logger,
	callbacks,
	configuration,
}: RunTaskOptions<T>) => {
	// 从jobPayload中获取仓库信息，动态确定工作目录
	const repoInfo = getRepoInfoFromPayload(jobPayload)
	const workspacePath = await setupWorkspace(repoInfo, logger)

	const ipcSocketPath = path.resolve(os.tmpdir(), `${crypto.randomUUID().slice(0, 8)}.sock`)
	const env = { ZHANLU_IPC_SOCKET_PATH: ipcSocketPath }
	const controller = new AbortController()
	const cancelSignal = controller.signal
	const containerized = isDockerContainer()

	const codeCommand = containerized
		? `xvfb-run --auto-servernum --server-num=1 code --wait --log trace --disable-workspace-trust --disable-gpu --disable-lcd-text --no-sandbox --user-data-dir /roo/.vscode --password-store="basic" -n ${workspacePath}`
		: `code --disable-workspace-trust -n ${workspacePath}`

	logger.info(codeCommand)

	// Sleep for a random amount of time between 5 and 10 seconds, unless we're
	// running in a container, in which case there are no issues with flooding
	// VSCode with new windows.
	if (!containerized) {
		await new Promise((resolve) => setTimeout(resolve, Math.random() * 5_000 + 5_000))
	}

	const subprocess = execa({ env, shell: "/bin/bash", cancelSignal })`${codeCommand}`

	// If debugging, add `--verbose` to `command` and uncomment the following line.
	// subprocess.stdout.pipe(process.stdout)

	// Give VSCode some time to spawn before connecting to its unix socket.
	await new Promise((resolve) => setTimeout(resolve, 3_000))
	let client: IpcClient | undefined = undefined
	let attempts = 5

	while (true) {
		try {
			client = new IpcClient(ipcSocketPath)
			await pWaitFor(() => client!.isReady, { interval: 250, timeout: 1_000 })
			break
		} catch (_error) {
			client?.disconnect()
			attempts--

			if (attempts <= 0) {
				logger.error(`unable to connect to IPC socket -> ${ipcSocketPath}`)
				throw new Error("Unable to connect.")
			}
		}
	}

	let taskStartedAt = Date.now()
	let taskFinishedAt: number | undefined
	let taskAbortedAt: number | undefined
	let taskTimedOut: boolean = false
	let rooTaskId: string | undefined
	let isClientDisconnected = false

	const slackNotifier = new SlackNotifier(logger)
	let slackThreadTs: string | null = null

	// 初始化消息桥接器
	const messageBridge = new MessageBridge()
	let isMessageBridgeActive = false

	const ignoreEvents: Record<"broadcast" | "log", RooCodeEventName[]> = {
		broadcast: [RooCodeEventName.Message],
		log: [RooCodeEventName.TaskTokenUsageUpdated, RooCodeEventName.TaskAskResponded],
	}

	client.on(IpcMessageType.TaskEvent, async (taskEvent: TaskEvent) => {
		const { eventName, payload } = taskEvent

		// Publish all events except for these to Redis.
		if (!ignoreEvents.broadcast.includes(eventName)) {
			await publish({ ...taskEvent })
		}

		// Log all events except for these.
		// For message events we only log non-partial messages.
		if (
			!ignoreEvents.log.includes(eventName) &&
			(eventName !== RooCodeEventName.Message || payload[0].message.partial !== true)
		) {
			logger.info(`${eventName} ->`, payload)
		}

		if (eventName === RooCodeEventName.TaskStarted) {
			taskStartedAt = Date.now()
			rooTaskId = payload[0]

			if (rooTaskId) {
				slackThreadTs = await slackNotifier.postTaskStarted({ jobType, jobPayload, rooTaskId })

				// 启动消息监控
				try {
					await messageBridge.startWatching(rooTaskId)
					isMessageBridgeActive = true
					logger.info(`Started message bridge for task ${rooTaskId}`)
				} catch (error) {
					logger.error(`Failed to start message bridge for task ${rooTaskId}:`, error)
				}

				if (callbacks?.onTaskStarted) {
					await callbacks.onTaskStarted(slackThreadTs, rooTaskId)
				}
			}
		}

		if (eventName === RooCodeEventName.TaskAborted) {
			taskAbortedAt = Date.now()

			// 停止消息监控
			if (isMessageBridgeActive && rooTaskId) {
				try {
					await messageBridge.stopWatching(rooTaskId)
					isMessageBridgeActive = false
					logger.info(`Stopped message bridge for task ${rooTaskId}`)
				} catch (error) {
					logger.error(`Failed to stop message bridge for task ${rooTaskId}:`, error)
				}
			}

			if (slackThreadTs) {
				await slackNotifier.postTaskUpdated(slackThreadTs, "Task was aborted", "warning")
			}

			if (callbacks?.onTaskAborted) {
				await callbacks.onTaskAborted(slackThreadTs)
			}
		}

		if (eventName === RooCodeEventName.TaskCompleted) {
			taskFinishedAt = Date.now()

			// 停止消息监控
			if (isMessageBridgeActive && rooTaskId) {
				try {
					await messageBridge.stopWatching(rooTaskId)
					isMessageBridgeActive = false
					logger.info(`Stopped message bridge for task ${rooTaskId}`)
				} catch (error) {
					logger.error(`Failed to stop message bridge for task ${rooTaskId}:`, error)
				}
			}

			if (slackThreadTs) {
				await slackNotifier.postTaskCompleted(slackThreadTs, true, taskFinishedAt - taskStartedAt, rooTaskId)
			}

			if (callbacks?.onTaskCompleted) {
				await callbacks.onTaskCompleted(slackThreadTs, true, taskFinishedAt - taskStartedAt, rooTaskId)
			}
		}
	})

	client.on(IpcMessageType.Disconnect, async () => {
		logger.info(`disconnected from IPC socket -> ${ipcSocketPath}`)
		isClientDisconnected = true
	})

	// 处理完整的 profile 配置
	let finalConfiguration = Object.assign({}, EVALS_SETTINGS, {
		openRouterApiKey: process.env.OPENROUTER_API_KEY,
		// 强制禁用VS Code shell integration，使用execa执行命令
		// 这可以避免shell integration相关的问题，特别是git hooks导致的卡住问题
		terminalShellIntegrationDisabled: true,
	})

	// 检查并导入完整的 profile 配置
	const profileConfigData = (jobPayload as any).profileConfiguration
	if (profileConfigData && typeof profileConfigData === "string") {
		try {
			logger.info(`[runTask] Found profile configuration, attempting to import...`)

			// 解析 profile 配置 JSON（使用插件标准格式）
			const settings = JSON.parse(profileConfigData)

			// 使用简化的配置应用函数
			const importResult = applyImportedConfiguration(settings)

			if (importResult) {
				const { configuration, profileName, provider } = importResult

				logger.info(`[runTask] Successfully imported profile: ${profileName} (${provider})`)

				// 直接使用导入的配置，无需复杂的适配逻辑
				finalConfiguration = mergeConfigurations(EVALS_SETTINGS, configuration)
			} else {
				logger.warn(`[runTask] Failed to apply imported configuration - invalid data structure`)
			}
		} catch (profileError) {
			logger.error(`[runTask] Failed to import profile configuration:`, profileError)
			logger.info(`[runTask] Falling back to default configuration`)
		}
	}

	// 如果没有完整的 profile 配置，或导入失败，则使用传递的 configuration 参数作为回退
	if (configuration?.apiProvider) {
		Object.assign(finalConfiguration, { apiProvider: configuration.apiProvider })
	}

	if (configuration?.modelName && configuration?.apiProvider) {
		// 使用通用函数设置模型字段
		setModelInConfig(finalConfiguration, configuration.modelName, configuration.apiProvider)
	}

	// 记录最终配置信息
	const finalApiProvider = finalConfiguration.apiProvider || "unknown"
	const finalModel = getModelFromConfig(finalConfiguration)

	logger.info(`[runTask] Final configuration - Provider: ${finalApiProvider}, Model: ${finalModel}`)
	logger.info(`[runTask] Final configuration allowedCommands: ${JSON.stringify(finalConfiguration.allowedCommands)}`)
	logger.info(`[runTask] Final configuration deniedCommands: ${JSON.stringify(finalConfiguration.deniedCommands)}`)
	logger.info(`[runTask] Final configuration alwaysAllowExecute: ${finalConfiguration.alwaysAllowExecute}`)
	logger.info(
		`[runTask] Final configuration alwaysAllowFollowupQuestions: ${finalConfiguration.alwaysAllowFollowupQuestions}`,
	)
	logger.info(
		`[runTask] Final configuration alwaysAllowUpdateTodoList: ${finalConfiguration.alwaysAllowUpdateTodoList}`,
	)
	logger.info(`[runTask] Final configuration commandExecutionTimeout: ${finalConfiguration.commandExecutionTimeout}`)
	logger.info(
		`[runTask] Final configuration preventCompletionWithOpenTodos: ${finalConfiguration.preventCompletionWithOpenTodos}`,
	)
	logger.info(`[runTask] Final configuration text: ${prompt}`)

	client.sendCommand({
		commandName: TaskCommandName.StartNewTask,
		data: {
			configuration: finalConfiguration,
			text: prompt,
			newTab: true,
		},
	})

	try {
		await pWaitFor(() => !!taskFinishedAt || !!taskAbortedAt || isClientDisconnected, {
			interval: 1_000,
			timeout: TIMEOUT,
		})
	} catch (_error) {
		taskTimedOut = true
		logger.error("time limit reached")

		if (slackThreadTs) {
			await slackNotifier.postTaskUpdated(slackThreadTs, "Task timed out after 30 minutes", "error")
		}

		if (callbacks?.onTaskTimedOut) {
			await callbacks.onTaskTimedOut(slackThreadTs)
		}

		if (rooTaskId && !isClientDisconnected) {
			logger.info("cancelling task")
			client.sendCommand({ commandName: TaskCommandName.CancelTask, data: rooTaskId })
			await new Promise((resolve) => setTimeout(resolve, 5_000)) // Allow some time for the task to cancel.
		}

		taskFinishedAt = Date.now()
	}

	if (!taskFinishedAt && !taskTimedOut) {
		logger.error("client disconnected before task finished")

		if (slackThreadTs) {
			await slackNotifier.postTaskUpdated(slackThreadTs, "Client disconnected before task completion", "error")
		}

		if (callbacks?.onClientDisconnected) {
			await callbacks.onClientDisconnected(slackThreadTs)
		}

		throw new Error("Client disconnected before task completion.")
	}

	if (rooTaskId && !isClientDisconnected) {
		logger.info("closing task")
		client.sendCommand({ commandName: TaskCommandName.CloseTask, data: rooTaskId })
		await new Promise((resolve) => setTimeout(resolve, 2_000)) // Allow some time for the window to close.
	}

	if (!isClientDisconnected) {
		logger.info("disconnecting client")
		client.disconnect()
	}

	logger.info("waiting for subprocess to finish")
	controller.abort()

	// Wait for subprocess to finish gracefully, with a timeout.
	const SUBPROCESS_TIMEOUT = 10_000

	try {
		await Promise.race([
			subprocess,
			new Promise((_, reject) =>
				setTimeout(() => reject(new SubprocessTimeoutError(SUBPROCESS_TIMEOUT)), SUBPROCESS_TIMEOUT),
			),
		])

		logger.info("subprocess finished gracefully")
	} catch (error) {
		if (error instanceof SubprocessTimeoutError) {
			logger.error("subprocess did not finish within timeout, force killing")

			try {
				if (subprocess.kill("SIGKILL")) {
					logger.info("SIGKILL sent to subprocess")
				} else {
					logger.error("failed to send SIGKILL to subprocess")
				}
			} catch (killError) {
				logger.error("subprocess.kill(SIGKILL) failed:", killError)
			}
		} else {
			throw error
		}
	}

	// 确保清理消息监控
	if (isMessageBridgeActive && rooTaskId) {
		try {
			await messageBridge.stopWatching(rooTaskId)
			logger.info(`Final cleanup: stopped message bridge for task ${rooTaskId}`)
		} catch (error) {
			logger.error(`Final cleanup: failed to stop message bridge for task ${rooTaskId}:`, error)
		}
	}

	logger.close()
}
