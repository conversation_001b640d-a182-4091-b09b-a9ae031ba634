import { eq } from "drizzle-orm"
import { Job } from "bullmq"

import { db, cloudJobs, type UpdateCloudJob } from "@/db"
import type { JobType, JobStatus, JobParams, JobPayload } from "@/types"

import { fixGitHubIssue } from "./jobs/fixGitHubIssue"
import { fixGitLabJiraIssue } from "./jobs/fixGitLabJiraIssue"
import { handleRoomoteCustomTask } from "./jobs/roomoteCustomTask"

export async function processJob<T extends JobType>({ data: { type, payload, jobId }, ...job }: Job<JobParams<T>>) {
	console.log(`[${job.name} | ${job.id}] Processing job ${jobId} of type ${type}`)

	try {
		let result: unknown

		switch (type) {
			case "github.issue.fix":
				result = await fixGitHubIssue(payload as JobPayload<"github.issue.fix">, {
					onTaskStarted: async (slackThreadTs: string | null, rooTaskId: string) => {
						// 更新任务状态并保存真实的task ID
						await updateJobStatus(
							jobId,
							"processing",
							undefined,
							undefined,
							slackThreadTs ?? undefined,
							rooTaskId,
						)
					},
				})
				break

			case "gitlab.jira.fix":
				result = await fixGitLabJiraIssue(payload as JobPayload<"gitlab.jira.fix">, {
					onTaskStarted: async (slackThreadTs: string | null, rooTaskId: string) => {
						// 更新任务状态并保存真实的task ID
						await updateJobStatus(
							jobId,
							"processing",
							undefined,
							undefined,
							slackThreadTs ?? undefined,
							rooTaskId,
						)
					},
				})
				break

			case "roomote.custom":
				result = await handleRoomoteCustomTask(payload as JobPayload<"roomote.custom">, {
					onTaskStarted: async (slackThreadTs: string | null, rooTaskId: string) => {
						// 更新任务状态并保存真实的task ID
						await updateJobStatus(
							jobId,
							"processing",
							undefined,
							undefined,
							slackThreadTs ?? undefined,
							rooTaskId,
						)
					},
				})
				break

			default:
				throw new Error(`Unknown job type: ${type}`)
		}

		await updateJobStatus(jobId, "completed", result)
		console.log(`[${job.name} | ${job.id}] Job ${jobId} completed successfully`)
	} catch (error) {
		console.error(`[${job.name} | ${job.id}] Job ${jobId} failed:`, error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		await updateJobStatus(jobId, "failed", undefined, errorMessage)
		throw error // Re-throw to mark job as failed in BullMQ.
	}
}

async function updateJobStatus(
	jobId: number,
	status: JobStatus,
	result?: unknown,
	error?: string,
	slackThreadTs?: string,
	taskId?: string,
) {
	const values: UpdateCloudJob = { status }

	if (status === "processing") {
		values.startedAt = new Date()
	} else if (status === "completed" || status === "failed") {
		values.completedAt = new Date()

		if (result) {
			values.result = result
		}

		if (error) {
			values.error = error
		}
	}

	if (slackThreadTs) {
		values.slackThreadTs = slackThreadTs
	}

	if (taskId) {
		values.taskId = taskId
	}

	await db.update(cloudJobs).set(values).where(eq(cloudJobs.id, jobId))
}
