import * as path from "path"
import * as os from "node:os"

import type { JobType, JobPayload } from "@/types"

import { runTask, type RunTaskCallbacks } from "../runTask"
import { Logger } from "../logger"

const jobType: JobType = "roomote.custom"

type RoomoteCustomTaskPayload = JobPayload<"roomote.custom">

export async function handleRoomoteCustomTask(
	jobPayload: RoomoteCustomTaskPayload,
	callbacks?: RunTaskCallbacks,
): Promise<{
	customInstructions: string
	gitlabRepo: string
	result: unknown
}> {
	const { customInstructions, gitlab } = jobPayload

	// 设置默认的JIRA ticket，用于commit message规范
	const defaultJiraTicket = "YDYCMKK-297"

	const prompt = `
Execute the following custom instructions on the GitLab repository:

GitLab Repository: ${gitlab.repo}
Repository Name: ${gitlab.repoName}
Branch: ${gitlab.branch}
Default Branch: ${gitlab.defaultBranch}
Repository URL: ${gitlab.webUrl}

Custom Instructions:
${customInstructions}

Please follow these steps:
1. Clone the GitLab repository: ${gitlab.repo}
2. Switch to the specified branch: ${gitlab.branch}
3. Analyze the custom instructions and implement the required changes
4. Ensure your solution addresses the requirements described in the instructions
5. Run any existing tests to verify your changes don't break anything
6. Commit your changes with proper format:

   Git Commit Message Format (Required):
   
   Subject Line: Fix ${defaultJiraTicket}: Custom task implementation
   
   Body Format:
   Code Source From: Self Code
   Description: [详细描述修改内容和实现方案]
   Jira: #${defaultJiraTicket}
   Custom Task: ${customInstructions.substring(0, 100)}${customInstructions.length > 100 ? "..." : ""}

   Example commit command:
   git commit -m "Fix ${defaultJiraTicket}: Custom task implementation

Code Source From: Self Code
Description: 根据自定义指令实现了相应功能，完成了指定的任务需求
Jira: #${defaultJiraTicket}
Custom Task: ${customInstructions.substring(0, 100)}${customInstructions.length > 100 ? "..." : ""}"

7. Push your branch: git push --set-upstream origin ${gitlab.branch}
8. Create a Merge Request that:
   - References the JIRA ticket ${defaultJiraTicket}
   - Has a clear title and description
   - Explains the changes made
   - References the custom instructions

GitLab Configuration:
- Repository: ${gitlab.repo}
- Base URL: ${gitlab.webUrl}
- Target Branch: ${gitlab.defaultBranch}

JIRA Integration:
- Make sure to reference ticket ${defaultJiraTicket} in commit messages using the required format
- Follow the specified commit message structure with "Code Source From: Self Code" and "Jira: #${defaultJiraTicket}"
- Include "Closes ${defaultJiraTicket}" or "Fixes ${defaultJiraTicket}" in the MR description

Your job isn't complete until you've successfully created the Merge Request and verified it's properly linked to the JIRA ticket.

Important Notes:
- Use GitLab CLI (glab) or GitLab API for creating the Merge Request
- Ensure proper authentication is configured for GitLab and JIRA
- Test your changes thoroughly before creating the MR
- Follow the repository's coding standards and contribution guidelines
- Implement the custom instructions while ensuring they follow standard development practices
`.trim()

	// 构建配置，使用传递的参数
	const configuration = {
		apiProvider: jobPayload.apiProvider || "zhanlu",
		modelName: jobPayload.modelName || "zhanluAI",
	}

	console.log(`[${jobType}] Using configuration:`, configuration)

	const result = await runTask({
		jobType,
		jobPayload,
		prompt,
		configuration,
		publish: async () => {},
		logger: new Logger({ logDir: path.resolve(os.tmpdir(), "logs"), filename: "worker.log", tag: "worker" }),
		callbacks,
	})

	return {
		customInstructions,
		gitlabRepo: gitlab.repo,
		result,
	}
}
