import * as path from "path"
import * as os from "node:os"

import type { JobType, JobPayload } from "@/types"

import { runTask, type RunTaskCallbacks } from "../runTask"
import { Logger } from "../logger"

const jobType: JobType = "gitlab.jira.fix"

type FixGitLabJiraIssueJobPayload = JobPayload<"gitlab.jira.fix">

export async function fixGitLabJiraIssue(
	jobPayload: FixGitLabJiraIssueJobPayload,
	callbacks?: RunTaskCallbacks,
): Promise<{
	jiraTicket: string
	gitlabRepo: string
	result: unknown
}> {
	const { jira, gitlab, customInstructions } = jobPayload

	// 设置默认的JIRA ticket，如果没有提供JIRA信息则使用默认值
	const defaultJiraTicket = "YDYCMKK-297"
	const actualJiraTicket = jira?.ticket || defaultJiraTicket
	const actualJiraSummary = jira?.summary || "Issue resolution"

	// 生成feature分支名称
	const featureBranchName = `feature_${actualJiraTicket}_remote-agent`
	const targetBranch = gitlab.branch || gitlab.defaultBranch

	let prompt = `Fix the following JIRA issue and create a GitLab Merge Request:

JIRA Ticket: ${actualJiraTicket}
Title: ${actualJiraSummary}
Status: ${jira?.status || "In Progress"}
Priority: ${jira?.priority || "Medium"}
Assignee: ${jira?.assignee || "Not Assigned"}

Description:
${jira?.description || "Please implement the necessary fixes based on the ticket requirements."}
IMPORTANT WORKING NOTE:
1. You must work in the current mode, do not switch to other modes
2. If you're in an offline environment, please skip installing dependencies or accessing the internet.
`

	// 如果有自定义指令，添加到prompt中
	if (customInstructions) {
		prompt += "\n\nAdditional Custom Instructions:\n" + customInstructions
	}

	prompt += `\n\nGitLab Repository: ${gitlab.repo}
Repository Name: ${gitlab.repoName}
Target Branch for MR: ${targetBranch}
Default Branch: ${gitlab.defaultBranch}
Repository URL: ${gitlab.webUrl}

Please analyze the JIRA issue`

	prompt += `, understand what needs to be fixed, and implement a solution in the GitLab repository.

Key Requirements:
1. Checkout the target branch: ${targetBranch}
2. Create a new feature branch: ${featureBranchName}
3. Switch to the feature branch for development
4. Analyze the issue description`

	prompt += " and implement the necessary changes\n"
	prompt += `5. Ensure your solution addresses the problem described in the JIRA ticket`

	prompt += `\n6. Commit your changes with proper format:

   Git Commit Message Format (Required):
   
   Subject Line: Fix ${actualJiraTicket}: ${actualJiraSummary}
   
   Body Format:
   Code Source From: Self Code
   Description: [详细描述修改内容和解决方案]
   Jira: #${actualJiraTicket}`

	prompt += `\n\n   Example commit command:
   git commit --no-verify -m "Fix ${actualJiraTicket}: ${actualJiraSummary}

Code Source From: Self Code
Description: 修复了${actualJiraSummary}相关问题，实现了相应的解决方案`

	prompt += `\nJira: #${actualJiraTicket}`

	prompt += `"

7. Push your feature branch: git push --set-upstream origin ${featureBranchName}
8. Create a Merge Request that:
   - References the JIRA ticket ${actualJiraTicket}
   - Has a clear title and description
   - Explains the changes made
   - Links back to the original JIRA issue
   - Merges from ${featureBranchName} to ${targetBranch}`

	prompt += `\n\nGitLab Configuration:
- Repository: ${gitlab.repo}
- Base URL: ${gitlab.webUrl}
- Source Branch: ${featureBranchName} (new feature branch)
- Target Branch: ${targetBranch}

JIRA Integration:
- Make sure to reference ticket ${actualJiraTicket} in commit messages using the required format
- Follow the specified commit message structure with "Code Source From: Self Code" and "Jira: #${actualJiraTicket}"
- Include "Closes ${actualJiraTicket}" or "Fixes ${actualJiraTicket}" in the MR description

Your job isn't complete until you've successfully created the Merge Request and verified it's properly linked to the JIRA ticket.

Important Notes:
- Use GitLab CLI (glab) or GitLab API for creating the Merge Request
- Ensure proper authentication is configured for both GitLab and JIRA
- Test your changes thoroughly before creating the MR
- Follow the repository's coding standards and contribution guidelines`

	prompt += `

GitLab CLI (glab) Usage Instructions:
==================================

The GitLab authentication is pre-configured in the container. You can use glab commands directly:

1. Clone the repository:
   git clone ${gitlab.webUrl}/${gitlab.repo}.git /roo/workspace
   cd /roo/workspace

2. Checkout target branch and create feature branch:
   git checkout ${targetBranch}
   git pull origin ${targetBranch}
   git checkout -b ${featureBranchName}

3. Make your changes and commit:
   git add .
   git commit --no-verify -m "Fix ${actualJiraTicket}: ${actualJiraSummary}

Code Source From: Self Code
Description: [your detailed description here]
Jira: #${actualJiraTicket}
市场项目名称: [Optional]
"

4. Push the feature branch:
   git push --set-upstream origin ${featureBranchName}

5. Create a Merge Request using glab:
   glab mr create \\
     --title "Fix ${actualJiraTicket}: ${actualJiraSummary}" \\
     --description "Fixes JIRA ticket ${actualJiraTicket}

## Description
[Detailed description of changes]

## JIRA Reference
- Ticket: ${actualJiraTicket}
- Summary: ${actualJiraSummary}
- Status: ${jira?.status || "In Progress"}

## Changes Made
[List the specific changes made]

Closes ${actualJiraTicket}" \\
     --source-branch ${featureBranchName} \\
     --target-branch ${targetBranch} \\
     --assignee @me

Alternative glab commands for troubleshooting:
- Check authentication: glab auth status
- List projects: glab repo list
- View project info: glab repo view ${gitlab.repo}
- Create MR interactively: glab mr create (if automated creation fails)

Remember to verify the MR was created successfully and note the MR URL in your response.`

	// 构建配置
	const configuration = {
		apiProvider: jobPayload.apiProvider || "zhanlu",
		modelName: jobPayload.modelName || "zhanluAI",
	}

	console.log(`[${jobType}] Using configuration:`, configuration)

	const result = await runTask({
		jobType,
		jobPayload,
		prompt,
		configuration,
		publish: async () => {},
		logger: new Logger({ logDir: path.resolve(os.tmpdir(), "logs"), filename: "worker.log", tag: "worker" }),
		callbacks,
	})

	return {
		jiraTicket: actualJiraTicket,
		gitlabRepo: gitlab.repo,
		result,
	}
}
