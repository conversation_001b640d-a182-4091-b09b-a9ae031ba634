DATABASE_URL=postgresql://postgres:password@localhost:5433/cloud_agents
REDIS_URL=redis://localhost:6380

GH_WEBHOOK_SECRET=your-webhook-secret-here
GH_TOKEN=your-token-here

OPENROUTER_API_KEY=sk-or-v1-...

SLACK_API_TOKEN=xoxb-...

# GitLab + JIRA 工作流环境配置示例
# 复制此文件为 gitlab-jira.env 并填入真实的配置信息

# ===================================
# JIRA 系统配置
# ===================================

# JIRA系统地址（必需）
JIRA_URL=http://jira.cmss.com

# JIRA认证信息（必需）
JIRA_USERNAME=your_jira_username
JIRA_PASSWORD=your_jira_password

# ===================================
# GitLab 系统配置
# ===================================

# GitLab系统地址（必需）
GITLAB_URL=http://gitlab.cmss.com

# GitLab Personal Access Token（必需）
# 需要的权限：api, read_repository, write_repository
GITLAB_PAT=your_gitlab_personal_access_token

# ===================================
# Roomote 服务配置
# ===================================

# Roomote API服务地址（可选，默认：http://localhost:3001）
ROOMOTE_API_URL=http://localhost:3001

# ===================================
# AI 模型配置
# ===================================

# 默认API提供商（可选，默认：zhanlu）
DEFAULT_API_PROVIDER=zhanlu

# 默认模型ID（可选，默认：zhanluAI）
DEFAULT_MODEL_ID=zhanluAI

# ===================================
# 调试和日志配置
# ===================================

# 启用调试模式（可选，默认：false）
DEBUG=false

# 日志级别（可选，可选值：error, warn, info, debug）
LOG_LEVEL=info

# Zhanlu 日志目录（可选，默认：系统临时目录）
# 设置此变量可避免在 Git 工作区创建日志文件
ZHANLU_LOG_DIR=/var/log/roomote