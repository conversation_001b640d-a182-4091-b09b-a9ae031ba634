{"name": "@roo-code/roomote", "version": "0.0.0", "type": "module", "scripts": {"lint": "next lint", "check-types": "tsc --noEmit", "test": "vitest", "dev": "concurrently \"next dev --port 3001\" \"ngrok http 3001 --domain cte.ngrok.dev\"", "build": "next build", "start": "next start --port 3001", "clean": "rimraf .next .turbo", "drizzle-kit": "dotenvx run -f .env -- tsx node_modules/drizzle-kit/bin.cjs", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:push": "pnpm drizzle-kit push", "db:check": "pnpm drizzle-kit check", "db:studio": "pnpm drizzle-kit studio", "services:start": "docker compose up -d db redis dashboard", "services:stop": "docker compose down dashboard redis db", "worker": "dotenvx run -f .env -- tsx src/lib/worker.ts", "controller": "dotenvx run -f .env -- tsx src/lib/controller.ts", "dashboard": "tsx scripts/dashboard.ts"}, "dependencies": {"@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@bull-board/ui": "^6.10.1", "@gitbeaker/rest": "^42.5.0", "@roo-code/ipc": "workspace:^", "@roo-code/types": "workspace:^", "bullmq": "^5.37.0", "drizzle-orm": "^0.44.1", "execa": "^9.6.0", "express": "^5.1.0", "ioredis": "^5.4.3", "jira.js": "^5.2.1", "next": "^15.2.5", "p-wait-for": "^5.0.2", "postgres": "^3.4.7", "react": "^18.3.1", "react-dom": "^18.3.1", "zod": "^3.25.41"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/express": "^5.0.3", "@types/node": "^22.15.20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "concurrently": "^9.1.0", "drizzle-kit": "^0.31.1", "tsx": "^4.19.3", "vitest": "^2.1.8"}}