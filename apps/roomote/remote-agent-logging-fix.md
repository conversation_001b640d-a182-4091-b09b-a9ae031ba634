# Remote Agent 日志文件位置优化

## 问题描述

在 Remote Agent 功能提交任务时，会在当前 worker 的工作目录（Git 仓库根目录）创建 `zhanlu-messages.log` 文件，这会影响 Git 工作区的整洁性，可能导致日志文件被意外提交到版本控制系统。

## 问题原因

1. **触发条件**：Remote Agent 启动时设置了 `ZHANLU_IPC_SOCKET_PATH` 环境变量
2. **日志启用**：VS Code 扩展检测到该环境变量后启用日志记录功能
3. **文件位置**：日志文件被创建在 `getWorkspacePath()` 返回的工作目录中

```typescript
// src/extension.ts
const socketPath = process.env.ZHANLU_IPC_SOCKET_PATH
const enableLogging = typeof socketPath === "string"

// src/extension/api.ts
if (enableLogging) {
	this.logfile = path.join(getWorkspacePath(), "zhanlu-messages.log")
}
```

## 解决方案

### 1. 修改日志文件存储位置

将日志文件从 Git 工作区移动到系统临时目录或专门的日志目录：

```typescript
// 修改后的代码
if (enableLogging) {
	// 将日志文件放到系统临时目录，避免影响 Git 工作区
	const logDir = process.env.ZHANLU_LOG_DIR || os.tmpdir()
	const workspaceName = path.basename(getWorkspacePath()) || "unknown"
	this.logfile = path.join(logDir, `zhanlu-messages-${workspaceName}-${Date.now()}.log`)
}
```

### 2. 配置环境变量

在 Remote Agent worker 启动时设置专门的日志目录：

```typescript
// apps/roomote/src/lib/controller.ts
const dockerArgs = [
	// ... 其他配置
	// 设置日志目录，避免在 Git 工作区创建日志文件
	`-e ZHANLU_LOG_DIR=/var/log/roomote`,
	"-v /tmp/roomote:/var/log/roomote",
	// ... 其他配置
]
```

## 优化效果

1. **避免污染 Git 工作区**：日志文件不再出现在代码仓库中
2. **统一日志管理**：所有日志文件集中存储在 `/var/log/roomote` 目录
3. **文件名唯一性**：使用工作区名称和时间戳确保文件名唯一
4. **向后兼容**：如果未设置 `ZHANLU_LOG_DIR`，默认使用系统临时目录

## 配置说明

### 环境变量

- `ZHANLU_LOG_DIR`：指定日志文件存储目录（可选）
    - 默认值：系统临时目录 (`os.tmpdir()`)
    - Remote Agent 推荐值：`/var/log/roomote`

### 日志文件命名规则

```
zhanlu-messages-{workspaceName}-{timestamp}.log
```

例如：`zhanlu-messages-zhanlu-vs-1703123456789.log`

## 部署注意事项

1. 确保日志目录具有写入权限
2. 定期清理旧的日志文件以避免磁盘空间占用
3. 在 `.gitignore` 中添加 `zhanlu-messages*.log` 规则（作为额外保护）

## 测试验证

1. 启动 Remote Agent 任务
2. 检查工作区根目录不再有 `zhanlu-messages.log` 文件
3. 确认日志文件在指定的日志目录中正常创建
4. 验证日志内容正常记录任务执行过程
