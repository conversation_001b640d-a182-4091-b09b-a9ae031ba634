# Docker 主机映射配置指南

## 📋 概述

Roomote 项目支持通过环境变量对 Docker 容器的 hosts 映射进行精细控制。本指南涵盖静态容器（docker-compose服务）和动态容器（controller创建的worker）的配置方法。

## 🎯 核心功能

- ✅ 支持完全禁用 hosts 映射
- ✅ 支持使用预设的默认映射
- ✅ 支持完全自定义映射配置
- ✅ 支持静态和动态容器统一配置
- ✅ 完全向后兼容

## 🔧 环境变量

### ENABLE_HOST_MAPPING

控制是否启用 hosts 映射功能

- **类型**: `true` 或 `false`
- **默认值**: `false`
- **作用范围**: 影响动态创建的worker容器

### DOCKER_HOST_MAPPINGS

自定义 hosts 映射内容

- **格式**: JSON 数组，每个元素包含 `host` 和 `ip` 字段
- **默认值**: 使用内置的默认映射（见下方列表）
- **作用范围**: 仅影响动态创建的worker容器

## 🏷️ 默认域名映射列表

当启用hosts映射时，支持以下域名解析：

```
download.bt.cn              → **************
dg2.bt.cn                   → **********
mirrors.tuna.tsinghua.edu.cn → ************
nexus.cmss.com              → **************
mirrors.bclinux.org         → ************
mirrors.bclinux.cn          → ************
mirrors.cmecloud.cn         → ************
cicdcsy.harbor.cmss.com     → **************
registry.paas               → **********
jira.cmss.com               → **************
conf.cmss.com               → **************
gitlab.cmss.com             → **************
gerrit.cmss.com             → **************
dev.idaas.cmss.com          → *************
aicp.idaas.cmss.com         → *************
gitlab.example2000.com      → ************
gerrit.cmssslave2.com       → ***********
SZYFQ-POD1-P0F0-PM-OS01-DATAINSIGHT-WEB-001 → 127.0.0.1
```

## 🚀 快速开始

### 方法 1: 使用预配置文件（推荐）

```bash
# 启用默认hosts映射的完整环境
docker-compose -f docker-compose-hosts.yml up

# 标准环境（动态容器无hosts映射）
docker-compose up
```

### 方法 2: 环境变量控制

```bash
# 1. 完全禁用动态容器hosts映射
export ENABLE_HOST_MAPPING=false
docker-compose up

# 2. 启用默认hosts映射
export ENABLE_HOST_MAPPING=true
docker-compose up

# 3. 使用自定义hosts映射
export ENABLE_HOST_MAPPING=true
export DOCKER_HOST_MAPPINGS='[
  {"host": "gitlab.company.com", "ip": "**********"},
  {"host": "nexus.company.com", "ip": "**********"}
]'
docker-compose up
```

### 方法 3: .env 文件配置

在项目根目录创建 `.env` 文件：

```bash
# .env 文件内容
ENABLE_HOST_MAPPING=true
DOCKER_HOST_MAPPINGS=[{"host": "gitlab.company.com", "ip": "**********"}]
```

## 📊 配置作用范围

### 静态容器（docker-compose服务）

以下服务的hosts映射通过 `docker-compose.yml` 中的 `extra_hosts` 配置：

- **db** (PostgreSQL数据库)
- **redis** (Redis缓存)
- **dashboard** (监控面板)
- **api** (API服务)
- **controller** (控制器服务)
- **worker** (静态工作节点)

### 动态容器（controller创建的worker）

- 通过环境变量 `ENABLE_HOST_MAPPING` 和 `DOCKER_HOST_MAPPINGS` 控制
- 在 `src/lib/controller.ts` 中实现
- 动态添加 `--add-host` 参数

## 🔄 部署和验证

### 部署步骤

```bash
cd apps/roomote

# 停止现有服务
docker-compose down

# 根据需要选择配置方式启动
docker-compose up -d
# 或
docker-compose -f docker-compose-hosts.yml up -d
```

### 验证配置

```bash
# 使用验证脚本（如果存在）
./scripts/verify-hosts.sh

# 手动验证静态容器
docker-compose exec api getent hosts gitlab.cmss.com
docker-compose exec worker getent hosts jira.cmss.com

# 查看动态容器配置（检查controller日志）
docker-compose logs controller | grep "add-host"
```

## 📋 使用场景选择

| 场景                         | 推荐方法      | 配置                                            |
| ---------------------------- | ------------- | ----------------------------------------------- |
| 新环境部署（无特殊网络需求） | 默认配置      | `docker-compose up`                             |
| 需要访问内网服务             | hosts配置文件 | `docker-compose -f docker-compose-hosts.yml up` |
| 自定义网络环境               | 环境变量      | 设置 `DOCKER_HOST_MAPPINGS`                     |
| 开发测试环境                 | .env文件      | 在项目根目录创建 `.env`                         |

## 🔧 高级配置

### 混合配置示例

```bash
# 部分使用默认映射 + 自定义映射
export ENABLE_HOST_MAPPING=true
export DOCKER_HOST_MAPPINGS='[
  {"host": "gitlab.cmss.com", "ip": "**************"},
  {"host": "jira.cmss.com", "ip": "**************"},
  {"host": "my-custom-host.com", "ip": "*************"}
]'
```

### 环境变量优先级

1. 系统环境变量（`export` 设置）
2. `.env` 文件中的变量
3. `docker-compose.yml` 中的默认值

## 🆘 故障排除

### 常见问题

**Q: 动态worker容器没有hosts映射？**

```bash
# 检查环境变量设置
echo $ENABLE_HOST_MAPPING
# 确保设置为 true
```

**Q: JSON格式错误？**

```bash
# 验证JSON格式
echo $DOCKER_HOST_MAPPINGS | python -m json.tool
```

**Q: 域名解析失败？**

```bash
# 检查IP地址可达性
ping **************
# 验证容器内解析
docker-compose exec api nslookup gitlab.cmss.com
```

### 调试步骤

1. **检查controller日志**

    ```bash
    docker-compose logs controller
    ```

2. **验证环境变量**

    ```bash
    docker-compose exec controller env | grep HOST
    ```

3. **测试网络连通性**
    ```bash
    docker-compose exec api ping gitlab.cmss.com
    ```

## 📚 技术实现

### Docker Compose配置

静态容器通过 `extra_hosts` 配置：

```yaml
services:
    api:
        extra_hosts:
            - "gitlab.cmss.com:**************"
            - "jira.cmss.com:**************"
```

### Controller动态配置

在 `src/lib/controller.ts` 中通过 `--add-host` 参数：

```typescript
const dockerArgs = ["--add-host", "gitlab.cmss.com:**************", "--add-host", "jira.cmss.com:**************"]
```

## 🔄 维护更新

### 添加新域名映射

1. 更新 `docker-compose.yml` 中的 `extra_hosts`
2. 在默认映射列表中添加新条目
3. 重新部署服务
4. 验证配置生效

### 版本兼容性

✅ **向后兼容**: 不设置新环境变量时，行为保持不变
✅ **无缝升级**: 现有部署无需修改即可使用新功能
✅ **灵活配置**: 可根据不同环境需求选择合适的配置方式

---

**配置完成后，所有Docker容器都能正确解析自定义域名，支持访问内网服务和开发环境。**
