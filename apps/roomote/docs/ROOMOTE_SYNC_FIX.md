# RoomoteAgentView 模式和Profile同步修复

## 🔍 问题描述

在RoomoteAgentView和ChatView之间存在模式(mode)和配置(profile)不同步的问题：

### 问题1：状态不同步

RoomoteAgentView使用本地状态，ChatView使用全局状态，导致提交时使用的是ChatView的状态而不是用户在RoomoteAgentView中选择的状态。

### 问题2：RoomoteAgentChatView显示问题

新创建的任务进入聊天界面时不显示模式和profile选择器，只有从历史记录进入时才显示。

### 问题3：Profile显示"default"问题

用户在RoomoteAgentView中选择特定的profile（如"GPT-4"），点击"启动代理"后跳转到RoomoteAgentChatView，底部显示的profile是"default"而不是用户选择的profile名称。但从历史记录进入时显示正确。

## 🕵️ 根因分析

经过深入分析，发现问题有两个层面：

### 1. 前端状态管理不统一

- RoomoteAgentView使用本地状态 `selectedMode` 和 `selectedProfile`
- ChatView使用全局状态 `mode` 和 `currentApiConfigName`
- 两个组件的状态没有同步

### 2. 前端解析后端数据的方式错误

- **前端期望**：从 `taskInfo.config` 获取配置信息
- **后端实际**：配置信息存储在 `taskInfo.payload` 中
- **结果**：前端获取不到配置，回退到硬编码的 `"default"`

#### 数据流程分析

- ✅ 前端正确发送：`profileName: currentApiConfigName`
- ✅ 后端正确存储：配置信息存储在 `payload.profileConfiguration` 中
- ✅ 后端正确返回：通过 `/api/jobs/{id}` 返回完整的 `payload` 数据
- ❌ 前端解析错误：期望从 `taskInfo.config` 获取，但实际在 `taskInfo.payload` 中

#### 配置信息存储位置

- **mode**: 存储在 `taskInfo.payload.mode`
- **profileName**: 需要从 `taskInfo.payload.profileConfiguration` JSON字符串中解析

#### 错误的解析逻辑示例

```typescript
// ❌ 错误：RoomoteAgentChatView 期望从这里获取配置
if (taskInfo?.config) {
	return taskInfo.config // 后端没有返回 config 字段
}
```

#### 正确的数据结构

```typescript
// ✅ 后端实际返回的结构
{
    id: job.id,
    type: job.type,
    status: job.status,
    payload: job.payload,  // 配置信息在这里
    // ...
}
```

## 🛠️ 解决方案

### 1. 统一状态管理

**修改文件**: `webview-ui/src/components/roomote/RoomoteAgentView.tsx`

- 移除本地状态 `selectedMode` 和 `selectedProfile`
- 使用全局状态 `mode`, `setMode`, `currentApiConfigName`, `setCurrentApiConfigName`
- 确保模式和配置选择器直接操作全局状态

**关键修改**:

```typescript
// 之前：使用本地状态
const [selectedMode, setSelectedMode] = useState<Mode>("code")
const [selectedProfile, setSelectedProfile] = useState<string>("")

// 之后：使用全局状态
const { customModes, mode, setMode, currentApiConfigName, setCurrentApiConfigName } = useExtensionState()
```

### 2. 同步状态更新

**模式选择器修改**:

```typescript
<SelectDropdown
    value={mode}
    onChange={(value) => {
        setMode(value as Mode)
        // 同步到扩展状态
        vscode.postMessage({ type: "mode", text: value })
    }}
    // ...其他props
/>
```

**Profile选择器修改**:

```typescript
<SelectDropdown
    value={currentApiConfigName || ""}
    onChange={(profileName) => {
        setCurrentApiConfigName(profileName)
        // 切换全局 API 配置，保持与 ChatView 的一致性
        const configMeta = listApiConfigMeta?.find(
            (config) => config.name === profileName,
        )
        if (configMeta) {
            vscode.postMessage({
                type: "loadApiConfigurationById",
                text: configMeta.id,
            })
        }
    }}
    // ...其他props
/>
```

### 3. 修复任务创建时的配置传递

**修改文件**: `webview-ui/src/components/roomote/RoomoteAgentView.tsx`

确保创建任务时使用正确的全局状态：

```typescript
const taskPayload: RoomoteTaskPayload = {
	// ...其他配置
	profileName: currentApiConfigName || undefined,
	mode: mode,
}
```

### 4. 修复RoomoteAgentChatView显示问题

**修改文件**: `webview-ui/src/components/roomote/RoomoteAgentChatView.tsx`

1. **移除isReadOnly限制**：确保配置信息始终显示

```typescript
// 之前：只在只读模式下显示
{isReadOnly && (taskConfig.mode || taskConfig.profileName) && (

// 之后：始终显示（如果有配置信息）
{(taskConfig.mode || taskConfig.profileName || modeDisplayName) && (
```

2. **修复配置解析逻辑**：正确解析后端返回的配置信息

**修复前（错误的解析逻辑）**：

```typescript
const taskConfig = useMemo(() => {
	if (historyItem?.remoteConfig) {
		return historyItem.remoteConfig
	}
	if (taskInfo?.config) {
		// ❌ 错误：后端没有返回config字段
		return taskInfo.config
	}
	// 回退到硬编码的"default"
	return {
		mode: mode,
		profileName: "default", // ❌ 硬编码导致显示错误
	}
}, [historyItem, taskInfo, mode, currentApiConfigName])
```

**修复后（正确的解析逻辑）**：

```typescript
const taskConfig = useMemo(() => {
	if (historyItem?.remoteConfig) {
		return historyItem.remoteConfig
	}
	// ✅ 正确：从后端返回的taskInfo.payload中解析配置信息
	if (taskInfo?.payload) {
		// 解析profileName - 如果有profileConfiguration，尝试从中解析
		let profileName = currentApiConfigName // 默认使用当前全局配置

		if (taskInfo.payload.profileConfiguration) {
			try {
				const profileConfig = JSON.parse(taskInfo.payload.profileConfiguration)
				// 从配置中提取profile名称
				profileName = profileConfig.profileName || profileConfig.name || currentApiConfigName
			} catch (error) {
				console.warn("Failed to parse profileConfiguration:", error)
			}
		}

		return {
			mode: taskInfo.payload.mode || mode, // ✅ 从payload中获取mode
			profileName: profileName, // ✅ 从profileConfiguration中解析profileName
		}
	}
	// 如果没有配置信息，使用当前全局状态作为默认值
	return {
		mode: mode,
		profileName: currentApiConfigName, // ✅ 使用全局状态而不是硬编码
	}
}, [historyItem, taskInfo, mode, currentApiConfigName])
```

### 5. 更新组件调用

**修改文件**: `webview-ui/src/App.tsx`

移除不再需要的currentApiConfigName参数：

```typescript
// 之前
<RoomoteAgentView
    onDone={() => switchTab("chat")}
    currentApiConfigName={currentApiConfigName}
    listApiConfigMeta={listApiConfigMeta}
    currentSection={currentSection}
/>

// 之后
<RoomoteAgentView
    onDone={() => switchTab("chat")}
    listApiConfigMeta={listApiConfigMeta}
    currentSection={currentSection}
/>
```

## 🎯 修改效果

### 修复前的问题

1. 用户在RoomoteAgentView选择"GPT-4" profile
2. 点击"启动代理"创建任务
3. 跳转到聊天界面显示：`Mode: Code | Profile: default` ❌

### 修复后的效果

1. **状态同步**：RoomoteAgentView和ChatView现在使用相同的全局状态，确保选择的模式和配置一致
2. **配置显示**：RoomoteAgentChatView现在始终显示模式和配置信息，无论是新创建的任务还是从历史记录进入的任务
3. **正确显示**：用户在RoomoteAgentView选择"GPT-4" profile后，聊天界面正确显示：`Mode: Code | Profile: GPT-4` ✅
4. **用户体验**：用户在RoomoteAgentView中选择的模式和配置现在会正确地应用到任务创建和执行中

## 📝 关键洞察

1. **不要假设API返回结构**：前端代码假设后端返回`config`字段，但实际返回的是`payload`字段

2. **配置信息的存储格式**：
    - `mode` 直接存储在 `payload.mode`
    - `profileName` 需要从 `payload.profileConfiguration` JSON字符串中解析

3. **历史记录vs新任务的差异**：
    - 历史记录：配置存储在 `historyItem.remoteConfig` 中（已处理过的格式）
    - 新任务：配置存储在 `taskInfo.payload` 中（原始格式，需要解析）

4. **避免硬编码默认值**：使用全局状态作为回退值，而不是硬编码字符串

## ✅ 测试建议

1. 在RoomoteAgentView中选择不同的模式和配置
2. 创建新任务并验证聊天界面显示正确的配置信息
3. 确认提交的任务使用正确的模式和配置
4. 测试从历史记录进入的任务仍然正常显示配置信息
5. 验证没有配置信息时的回退行为
6. 测试profile名称解析的各种情况（有profileConfiguration、无profileConfiguration等）
