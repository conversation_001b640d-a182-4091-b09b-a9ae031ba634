# Remote Agent 聊天界面集成技术方案

## 📋 项目概述

本文档详细描述了如何将 Remote Agent 的聊天消息实时集成到 VS Code 插件的界面中，包括历史任务管理、消息数据获取、以及专门的聊天界面实现。

## 🏗️ 系统架构分析

### 当前架构

```mermaid
graph TB
    A[RoomoteAgentView] --> B[Roomote API]
    B --> C[Redis Queue]
    C --> D[Worker Container]
    D --> E[VS Code Instance]
    E --> F[Roo Code Extension]
    F --> G[ui_messages.json]

    H[插件历史记录] --> I[本地任务]
    J[ChatView.tsx] --> K[本地消息显示]
```

### 目标架构

```mermaid
graph TB
    A[RoomoteAgentView] --> B[Roomote API]
    B --> C[Redis Queue]
    C --> D[Worker Container]
    D --> E[VS Code Instance]
    E --> F[Roo Code Extension]
    F --> G[ui_messages.json]

    B --> L[消息代理API]
    G --> M[共享存储卷]
    M --> L
    L --> N[WebSocket/SSE]
    N --> O[RemoteAgentChatView]

    H[统一历史记录] --> P[本地 + Remote任务]
    P --> Q[历史任务查看器]
    Q --> O
```

## 🔧 技术方案详解

### 1. 历史任务集成方案

#### 1.1 扩展 HistoryItem 数据结构

```typescript
interface RemoteHistoryItem extends HistoryItem {
	// 新增字段
	taskType: "local" | "remote" // 任务类型标识
	remoteTaskId?: string // Remote Agent任务ID
	remoteJobId?: string // BullMQ任务ID
	roomoteApiUrl?: string // Roomote API地址
	status?: "pending" | "processing" | "completed" | "failed" // Remote任务状态

	// 现有字段保持不变
	id: string
	number: number
	ts: number
	task: string
	tokensIn: number
	tokensOut: number
	// ...其他现有字段
}
```

#### 1.2 历史记录管理器扩展

```typescript
// src/core/remoteAgent/RemoteTaskManager.ts
export class RemoteTaskManager {
	private apiClient: RoomoteApiClient

	constructor(private context: vscode.ExtensionContext) {
		this.apiClient = new RoomoteApiClient()
	}

	async fetchRemoteTaskHistory(): Promise<RemoteHistoryItem[]> {
		const remoteTasks = await this.apiClient.getTaskHistory()
		return remoteTasks.map((task) => this.convertToHistoryItem(task))
	}

	async syncRemoteTaskToHistory(taskId: string): Promise<void> {
		const taskData = await this.apiClient.getTaskById(taskId)
		const historyItem = this.convertToHistoryItem(taskData)

		// 集成到现有历史记录系统
		const provider = await this.getProvider()
		await provider.updateTaskHistory(historyItem)
	}

	private convertToHistoryItem(remoteTask: any): RemoteHistoryItem {
		return {
			id: `remote-${remoteTask.id}`,
			taskType: "remote",
			remoteTaskId: remoteTask.id,
			remoteJobId: remoteTask.jobId,
			roomoteApiUrl: remoteTask.apiUrl,
			status: remoteTask.status,
			number: remoteTask.number,
			ts: new Date(remoteTask.createdAt).getTime(),
			task: remoteTask.prompt || remoteTask.description,
			tokensIn: remoteTask.tokensUsed?.input || 0,
			tokensOut: remoteTask.tokensUsed?.output || 0,
			cacheWrites: 0,
			cacheReads: 0,
			totalCost: remoteTask.cost || 0,
			size: 0,
			workspace: remoteTask.workspace || "remote",
		}
	}
}
```

### 2. 消息数据获取方案

#### 2.1 Docker 配置修改

```yaml
# apps/roomote/docker-compose.yml (修改worker服务)
worker:
    build:
        context: ../../
        dockerfile: apps/roomote/Dockerfile.worker
    volumes:
        - /run/user/1000/docker.sock:/var/run/docker.sock
        - /tmp/roomote:/var/log/roomote
        # 新增：映射VS Code globalStorage目录
        - /tmp/roomote/vscode-storage:/roo/.vscode/User/globalStorage
        # 新增：映射消息共享目录
        - /tmp/roomote/messages:/roo/shared/messages
```

#### 2.2 消息同步机制

```typescript
// apps/roomote/src/lib/messageBridge.ts
export class MessageBridge {
	private watchedTasks = new Map<string, fs.FSWatcher>()

	constructor(private redis: Redis) {}

	async startWatching(taskId: string): Promise<void> {
		const messagesPath = `/roo/.vscode/User/globalStorage/ecloud.zhanlu/tasks/${taskId}/ui_messages.json`
		const sharedPath = `/roo/shared/messages/${taskId}.json`

		// 监控文件变化
		const watcher = fs.watch(messagesPath, async (eventType) => {
			if (eventType === "change") {
				try {
					const messages = await fs.readFile(messagesPath, "utf8")

					// 复制到共享目录
					await fs.writeFile(sharedPath, messages)

					// 推送到Redis以供API使用
					await this.redis.publish(`task:${taskId}:messages`, messages)

					console.log(`[MessageBridge] Synced messages for task ${taskId}`)
				} catch (error) {
					console.error(`[MessageBridge] Error syncing messages for task ${taskId}:`, error)
				}
			}
		})

		this.watchedTasks.set(taskId, watcher)
	}

	async stopWatching(taskId: string): Promise<void> {
		const watcher = this.watchedTasks.get(taskId)
		if (watcher) {
			watcher.close()
			this.watchedTasks.delete(taskId)
		}
	}
}
```

#### 2.3 API 消息服务

```typescript
// apps/roomote/src/app/api/tasks/[id]/messages/route.ts
import { NextRequest, NextResponse } from "next/server"
import { redis } from "@/lib/redis"
import fs from "fs/promises"
import path from "path"

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	const { id: taskId } = await params
	const { searchParams } = new URL(request.url)
	const stream = searchParams.get("stream") === "true"

	if (stream) {
		// 服务器发送事件(SSE)实现实时推送
		const encoder = new TextEncoder()
		const readable = new ReadableStream({
			start(controller) {
				const subscriber = redis.duplicate()
				subscriber.subscribe(`task:${taskId}:messages`)

				subscriber.on("message", (channel, message) => {
					const data = `data: ${message}\n\n`
					controller.enqueue(encoder.encode(data))
				})

				// 发送当前消息作为初始状态
				getTaskMessages(taskId).then((messages) => {
					const data = `data: ${JSON.stringify(messages)}\n\n`
					controller.enqueue(encoder.encode(data))
				})
			},
		})

		return new Response(readable, {
			headers: {
				"Content-Type": "text/event-stream",
				"Cache-Control": "no-cache",
				Connection: "keep-alive",
			},
		})
	} else {
		// 普通REST API返回当前消息
		const messages = await getTaskMessages(taskId)
		return NextResponse.json({ messages })
	}
}

async function getTaskMessages(taskId: string) {
	const messagesPath = path.join("/tmp/roomote/messages", `${taskId}.json`)

	try {
		const content = await fs.readFile(messagesPath, "utf8")
		return JSON.parse(content)
	} catch (error) {
		return []
	}
}
```

### 3. Remote Agent 专用聊天界面

#### 3.1 组件设计决策

基于分析，`ChatView.tsx` 包含过多本地任务特定的功能（如工具调用、自动审批、命令执行等），不适合直接复用。因此选择创建专门的 `RemoteAgentChatView` 组件。

#### 3.2 组件架构

```typescript
// webview-ui/src/components/remoteAgent/RemoteAgentChatView.tsx
export interface RemoteAgentChatViewProps {
  taskId: string
  roomoteApiUrl: string
  isReadOnly?: boolean
}

export const RemoteAgentChatView: React.FC<RemoteAgentChatViewProps> = ({
  taskId,
  roomoteApiUrl,
  isReadOnly = true
}) => {
  const [messages, setMessages] = useState<ClineMessage[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [taskStatus, setTaskStatus] = useState<'pending' | 'processing' | 'completed' | 'failed'>('pending')

  // 实时消息订阅
  useEffect(() => {
    const eventSource = new EventSource(`${roomoteApiUrl}/api/tasks/${taskId}/messages?stream=true`)

    eventSource.onopen = () => setIsConnected(true)
    eventSource.onmessage = (event) => {
      const newMessages = JSON.parse(event.data)
      setMessages(newMessages)
    }
    eventSource.onerror = () => setIsConnected(false)

    return () => eventSource.close()
  }, [taskId, roomoteApiUrl])

  return (
    <div className="remote-agent-chat-container">
      <RemoteAgentHeader
        taskId={taskId}
        status={taskStatus}
        isConnected={isConnected}
      />

      <RemoteAgentMessageList
        messages={messages}
        isReadOnly={isReadOnly}
      />

      {!isReadOnly && (
        <RemoteAgentInput
          onSendMessage={(message) => {
            // 发送消息到Remote Agent
          }}
        />
      )}
    </div>
  )
}
```

#### 3.3 子组件设计

```typescript
// webview-ui/src/components/remoteAgent/RemoteAgentMessageList.tsx
export const RemoteAgentMessageList: React.FC<{
  messages: ClineMessage[]
  isReadOnly: boolean
}> = ({ messages, isReadOnly }) => {
  const virtuosoRef = useRef<VirtuosoHandle>(null)

  // 复用现有的消息处理逻辑，但移除交互功能
  const processedMessages = useMemo(() => {
    return combineApiRequests(combineCommandSequences(messages))
  }, [messages])

  const itemContent = useCallback((index: number, message: ClineMessage) => {
    return (
      <RemoteAgentMessageRow
        key={message.ts}
        message={message}
        isLast={index === processedMessages.length - 1}
        isReadOnly={isReadOnly}
      />
    )
  }, [processedMessages.length, isReadOnly])

  return (
    <div className="remote-agent-messages">
      <Virtuoso
        ref={virtuosoRef}
        data={processedMessages}
        itemContent={itemContent}
        followOutput="smooth"
      />
    </div>
  )
}
```

#### 3.4 消息行组件

```typescript
// webview-ui/src/components/remoteAgent/RemoteAgentMessageRow.tsx
export const RemoteAgentMessageRow: React.FC<{
  message: ClineMessage
  isLast: boolean
  isReadOnly: boolean
}> = ({ message, isLast, isReadOnly }) => {
  // 复用现有ChatRow的渲染逻辑，但禁用所有交互功能
  return (
    <div className="remote-agent-message-row">
      {/* 复用ChatRow的消息显示逻辑 */}
      {message.type === 'say' && <RemoteAgentSayMessage message={message} />}
      {message.type === 'ask' && <RemoteAgentAskMessage message={message} isReadOnly={isReadOnly} />}
    </div>
  )
}
```

### 4. 插件端集成方案

#### 4.1 RoomoteAgentView 修改

```typescript
// webview-ui/src/components/roomote/RoomoteAgentView.tsx
export const RoomoteAgentView: React.FC = () => {
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  const [showChatView, setShowChatView] = useState(false)

  const handleStartAgent = async (config: AgentConfig) => {
    try {
      // 创建Remote Agent任务
      const response = await fetch(`${ROOMOTE_API_URL}/api/jobs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      const { jobId, enqueuedJobId } = await response.json()

      // 保存任务ID并跳转到聊天界面
      setCurrentTaskId(jobId)
      setShowChatView(true)

      // 同步到VS Code历史记录
      vscode.postMessage({
        type: 'syncRemoteTask',
        taskId: jobId,
        roomoteApiUrl: ROOMOTE_API_URL
      })

    } catch (error) {
      console.error('Failed to start Remote Agent:', error)
    }
  }

  if (showChatView && currentTaskId) {
    return (
      <RemoteAgentChatView
        taskId={currentTaskId}
        roomoteApiUrl={ROOMOTE_API_URL}
        isReadOnly={true}
      />
    )
  }

  return (
    <div className="roomote-agent-config">
      {/* 现有的配置界面 */}
      <button onClick={handleStartAgent}>
        启动代理
      </button>
    </div>
  )
}
```

#### 4.2 VS Code 扩展端处理

```typescript
// src/core/webview/webviewMessageHandler.ts
case "syncRemoteTask":
  const remoteTaskManager = new RemoteTaskManager(provider.context)
  await remoteTaskManager.syncRemoteTaskToHistory(message.taskId)
  await provider.postStateToWebview()
  break

case "openRemoteTaskChat":
  // 打开Remote Agent聊天界面
  const remoteTaskId = message.taskId
  await provider.postMessageToWebview({
    type: "showRemoteTaskChat",
    taskId: remoteTaskId,
    roomoteApiUrl: message.roomoteApiUrl
  })
  break
```

#### 4.3 历史记录界面修改

```typescript
// webview-ui/src/components/history/HistoryPreview.tsx
export const HistoryPreview: React.FC = () => {
  const { taskHistory } = useExtensionState()

  const handleTaskClick = (historyItem: RemoteHistoryItem) => {
    if (historyItem.taskType === 'remote') {
      // 打开Remote Agent聊天界面
      vscode.postMessage({
        type: 'openRemoteTaskChat',
        taskId: historyItem.remoteTaskId,
        roomoteApiUrl: historyItem.roomoteApiUrl
      })
    } else {
      // 现有的本地任务处理逻辑
      vscode.postMessage({
        type: 'showTaskWithId',
        text: historyItem.id
      })
    }
  }

  return (
    <div className="history-preview">
      {taskHistory.map(item => (
        <HistoryItem
          key={item.id}
          item={item}
          onClick={() => handleTaskClick(item)}
        />
      ))}
    </div>
  )
}
```

## 🚀 实施步骤

### 阶段一：基础设施准备（1-2天）

1. **修改Docker配置**
    - 更新 `docker-compose.yml` 添加消息共享卷
    - 修改 `Dockerfile.worker` 添加消息同步脚本

2. **创建消息代理服务**
    - 实现 `MessageBridge` 类
    - 添加消息同步API端点
    - 集成Redis消息发布

### 阶段二：历史记录集成（2-3天）

1. **扩展数据结构**
    - 修改 `HistoryItem` 接口
    - 实现 `RemoteTaskManager` 类
    - 更新历史记录显示逻辑

2. **API客户端开发**
    - 创建 `RoomoteApiClient` 类
    - 实现任务状态同步
    - 添加错误处理和重试机制

### 阶段三：聊天界面开发（3-4天）

1. **创建Remote Agent专用组件**
    - `RemoteAgentChatView` 主组件
    - `RemoteAgentMessageList` 消息列表
    - `RemoteAgentMessageRow` 消息行

2. **实现实时消息订阅**
    - SSE客户端连接
    - 消息状态管理
    - 错误处理和重连

### 阶段四：插件集成（2-3天）

1. **修改RoomoteAgentView**
    - 添加聊天界面跳转
    - 集成任务创建流程

2. **扩展消息处理**
    - 添加Remote任务相关消息类型
    - 实现历史记录同步

3. **更新历史记录界面**
    - 支持Remote任务显示
    - 添加任务类型标识

### 阶段五：测试和优化（2-3天）

1. **功能测试**
    - 端到端任务流程测试
    - 消息实时同步测试
    - 历史记录集成测试

2. **性能优化**
    - 消息传输性能优化
    - 内存使用优化
    - 用户体验优化

## 🔧 技术细节

### 消息格式兼容性

Remote Agent的 `ui_messages.json` 格式与现有的 `ClineMessage` 基本兼容：

```typescript
// Remote Agent 消息格式
interface RemoteAgentMessage {
	ts: number
	type: "say" | "ask"
	say?: "text" | "api_req_started" | "api_req_finished" | "tool" | "command"
	ask?: "tool" | "command" | "completion_result"
	text?: string
	partial?: boolean
	isProtected?: boolean
}

// 现有 ClineMessage 格式 (完全兼容)
```

### 性能考虑

1. **消息传输优化**
    - 使用增量更新而非全量同步
    - 实现消息去重和合并
    - 添加消息压缩

2. **内存管理**
    - 限制内存中保存的消息数量
    - 实现消息分页加载
    - 定期清理无用消息

3. **网络优化**
    - WebSocket连接池管理
    - 断线重连机制
    - 错误重试策略

### 安全考虑

1. **访问控制**
    - API端点权限验证
    - 任务所有权检查

2. **数据保护**
    - 敏感信息过滤
    - 消息内容加密传输

## 📚 配置说明

### 环境变量

```bash
# Roomote API 配置
ROOMOTE_API_URL=http://localhost:3001
ROOMOTE_REDIS_URL=redis://localhost:6379

# 消息同步配置
MESSAGE_SYNC_INTERVAL=1000  # 消息同步间隔(ms)
MESSAGE_BUFFER_SIZE=1000    # 消息缓冲区大小
```

### VS Code 设置

```json
{
	"rooCode.remoteAgent.enabled": true,
	"rooCode.remoteAgent.apiUrl": "http://localhost:3001",
	"rooCode.remoteAgent.autoSync": true,
	"rooCode.remoteAgent.syncInterval": 5000
}
```

## 🎯 预期效果

1. **统一的任务管理**
    - 本地和Remote任务在同一界面管理
    - 一致的历史记录查看体验

2. **实时消息同步**
    - Remote Agent执行过程实时可见
    - 无延迟的消息更新

3. **流畅的用户体验**
    - 从启动到查看的无缝衔接
    - 直观的任务状态显示

4. **可扩展的架构**
    - 支持未来更多Remote Agent功能
    - 易于维护和升级

## 🚨 风险和限制

### 技术风险

1. **网络连接稳定性**
    - SSE连接可能中断
    - 需要robust的重连机制

2. **消息同步延迟**
    - 文件系统监控延迟
    - 网络传输延迟

3. **资源占用**
    - 多个WebSocket连接
    - 消息缓存内存占用

### 解决方案

1. **连接管理**
    - 实现指数退避重连
    - 连接状态监控和恢复

2. **性能优化**
    - 消息批量处理
    - 智能缓存策略

3. **错误处理**
    - 详细的错误日志
    - 用户友好的错误提示

## 📈 后续扩展

1. **高级功能**
    - Remote Agent任务控制（暂停/恢复/取消）
    - 消息搜索和过滤
    - 任务执行统计和分析

2. **集成优化**
    - 与本地任务的更深度集成
    - 跨任务的上下文共享

3. **用户体验**
    - 更丰富的消息类型支持
    - 自定义界面布局
    - 快捷操作和键盘支持

---

本技术方案提供了完整的Remote Agent聊天界面集成解决方案，确保了功能的完整性、性能的可靠性和用户体验的流畅性。通过分阶段实施，可以有效控制开发风险并确保项目成功交付。
