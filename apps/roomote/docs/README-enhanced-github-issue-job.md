# 增强的GitHub Issue Job脚本功能说明

## 🎯 功能概述

`enqueue-github-issue-job.sh` 脚本已经增强，支持动态指定API provider和模型名称，默认使用zhanlu作为API provider。

## 🔧 主要功能

### 1. 动态API Provider配置

- 支持动态指定API provider（zhanlu、openrouter、anthropic、openai等）
- 支持动态指定具体的模型名称
- 默认使用zhanlu作为API provider，zhanluAI作为默认模型

### 2. 兼容性保持

- 保持与原脚本的完全向后兼容
- 所有原有功能继续正常工作
- 新增的参数为可选参数

## 📝 使用方法

### 基础语法

```bash
./enqueue-github-issue-job.sh <issue_number> [repo] [api_provider] [model_name]
```

### 参数说明

- `issue_number`: GitHub issue编号 (必需)
- `repo`: 仓库名称，格式为 'owner/repo' (可选，默认: village-way/Roo-Code)
- `api_provider`: API提供商 (可选，默认: zhanlu)
- `model_name`: 模型名称 (可选，默认: zhanluAI)

### 使用示例

#### 1. 使用默认配置 (zhanlu + zhanluAI)

```bash
./enqueue-github-issue-job.sh 123
```

#### 2. 指定自定义仓库，使用默认API配置

```bash
./enqueue-github-issue-job.sh 123 owner/repo
```

#### 3. 使用OpenRouter API

```bash
./enqueue-github-issue-job.sh 123 owner/repo openrouter gpt-4-turbo
```

#### 4. 使用Anthropic API

```bash
./enqueue-github-issue-job.sh 123 owner/repo anthropic claude-3-sonnet
```

#### 5. 使用默认仓库但指定API配置

```bash
./enqueue-github-issue-job.sh 123 "" zhanlu zhanluAI
```

## 🛠 技术实现

### 1. 脚本层面的改动

- 增加了`API_PROVIDER`和`MODEL_NAME`参数解析
- 增强了帮助信息显示
- 在JSON payload中添加了`apiProvider`和`modelName`字段

### 2. 后端类型定义更新

- 更新了`JobTypes`接口，添加了`apiProvider`和`modelName`可选字段
- 更新了相应的Zod schema验证

### 3. 任务处理流程增强

- 修改了`fixGitHubIssue`函数，传递配置信息到`runTask`
- 更新了`runTask`函数，支持接收和处理配置参数
- 实现了不同API provider的模型字段映射

### 4. 默认配置更新

- 修改了`EVALS_SETTINGS`，将默认的`apiProvider`从`openrouter`改为`zhanlu`
- 添加了默认的`zhanluModelId`设置

## 🔍 支持的API Providers

| Provider   | 模型字段名        | 示例模型        |
| ---------- | ----------------- | --------------- |
| zhanlu     | zhanluModelId     | zhanluAI        |
| openrouter | openRouterModelId | gpt-4-turbo     |
| anthropic  | apiModelId        | claude-3-sonnet |
| openai     | openAiModelId     | gpt-4           |
| gemini     | apiModelId        | gemini-pro      |
| ollama     | ollamaModelId     | llama2          |

## ✅ 测试验证

### 运行测试脚本

```bash
# 基础功能测试
./test-enhanced-script.sh

# JSON payload生成测试
./test-json-payload.sh
```

### 验证点

- ✅ 向后兼容性：原有调用方式继续工作
- ✅ 默认配置：使用zhanlu作为默认API provider
- ✅ 动态配置：支持指定任意API provider和模型
- ✅ JSON结构：payload中正确包含apiProvider和modelName字段
- ✅ 类型安全：后端类型定义正确处理新字段

## 🔄 完整执行流程

1. **脚本执行**: 解析命令行参数，构建包含API配置的JSON payload
2. **任务入队**: API endpoint验证并存储任务到数据库
3. **Worker处理**:
    - 读取任务配置
    - 根据apiProvider创建相应的profile
    - 根据modelName设置对应的模型字段
    - 启动VS Code实例执行任务
4. **任务执行**: 使用指定的API provider和模型处理GitHub issue

## 📊 示例JSON Payload

使用zhanlu配置时的payload结构：

```json
{
	"type": "github.issue.fix",
	"payload": {
		"repo": "village-way/Roo-Code",
		"issue": 123,
		"title": "Issue标题",
		"body": "Issue描述",
		"labels": ["bug", "enhancement"],
		"apiProvider": "zhanlu",
		"modelName": "zhanluAI"
	}
}
```

## 🚀 部署说明

修改涉及以下文件：

- `apps/roomote/scripts/enqueue-github-issue-job.sh`: 脚本增强
- `apps/roomote/src/types/index.ts`: 类型定义更新
- `apps/roomote/src/lib/jobs/fixGitHubIssue.ts`: 任务处理逻辑
- `apps/roomote/src/lib/runTask.ts`: 配置处理逻辑
- `packages/types/src/global-settings.ts`: 默认配置更新

确保重新构建相关模块后部署。
