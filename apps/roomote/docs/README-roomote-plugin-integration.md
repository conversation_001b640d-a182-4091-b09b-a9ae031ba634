# Roomote Agent 插件集成指南

## 📋 概述

本文档介绍 Roomote Agent API 的插件集成功能，包括自定义指令和指定模式支持。经过改造后，Roomote API 现在支持来自湛卢VS插件的任务类型，并可以根据用户在插件中的配置（profile、mode）智能执行任务。

## 🚀 新增功能

### 1. 支持的任务类型

Roomote API 现在支持以下任务类型：

| 任务类型           | 说明                          | 用途                                                     |
| ------------------ | ----------------------------- | -------------------------------------------------------- |
| `roomote.custom`   | 纯自定义指令任务              | 在没有JIRA连接时，仅使用自定义指令执行任务               |
| `gitlab.jira.fix`  | JIRA+GitLab集成任务（增强版） | 结合JIRA工单和自定义指令执行任务，支持指定分支和执行模式 |
| `github.issue.fix` | GitHub Issue修复              | 修复GitHub仓库中的Issue（现有功能）                      |

### 2. 智能配置管理

- **自动配置解析**: 插件端根据用户选择的profile自动解析apiProvider和modelName
- **模式支持**: 支持不同的执行模式（code、architect等）
- **分支管理**: 支持指定GitLab分支进行操作
- **向后兼容**: 保持对现有脚本的兼容性（支持modelId字段）

## 🔧 API 接口规范

### roomote.custom 任务

**用途**: 执行纯自定义指令任务

**请求示例**:

```json
{
	"type": "roomote.custom",
	"payload": {
		"customInstructions": "请在项目中添加一个新的工具函数...",
		"gitlab": {
			"repo": "company/project",
			"repoName": "project",
			"defaultBranch": "main",
			"webUrl": "https://gitlab.example.com/company/project",
			"branch": "feature/new-tool"
		},
		"mode": "code",
		"apiProvider": "zhanlu",
		"modelName": "zhanluAI"
	}
}
```

### gitlab.jira.fix 任务（增强版）

**用途**: JIRA工单修复 + 自定义指令支持

**请求示例**:

```json
{
	"type": "gitlab.jira.fix",
	"payload": {
		"jira": {
			"ticket": "PROJ-123",
			"summary": "修复登录问题",
			"description": "用户无法正常登录系统",
			"priority": "High",
			"assignee": "<EMAIL>",
			"status": "In Progress"
		},
		"gitlab": {
			"repo": "company/web-app",
			"repoName": "web-app",
			"defaultBranch": "develop",
			"webUrl": "https://gitlab.company.com/company/web-app",
			"branch": "fix/PROJ-123-login-issue"
		},
		"customInstructions": "请使用新的认证库，确保向后兼容",
		"mode": "code",
		"apiProvider": "zhanlu",
		"modelName": "zhanluAI"
	}
}
```

## 🔄 字段说明

### 公共字段

| 字段          | 类型   | 必需 | 说明                               |
| ------------- | ------ | ---- | ---------------------------------- |
| `apiProvider` | string | 可选 | API提供商（默认: "zhanlu"）        |
| `modelName`   | string | 可选 | 模型名称                           |
| `mode`        | string | 可选 | 执行模式（如 "code", "architect"） |

### GitLab配置

| 字段            | 类型   | 必需 | 说明         |
| --------------- | ------ | ---- | ------------ |
| `repo`          | string | 是   | 仓库完整路径 |
| `repoName`      | string | 是   | 仓库名称     |
| `defaultBranch` | string | 是   | 默认分支     |
| `webUrl`        | string | 是   | 仓库Web URL  |
| `branch`        | string | 可选 | 指定工作分支 |

### 自定义指令

| 字段                 | 类型   | 必需   | 说明           |
| -------------------- | ------ | ------ | -------------- |
| `customInstructions` | string | 视情况 | 自定义执行指令 |

## 🎯 插件集成要点

### 1. 配置管理

- 插件端根据用户选择的profile解析出 `apiProvider` 和 `modelName`
- 不需要传递profile名称到后端
- 支持mode选择（code模式、architect模式等）

### 2. 分支策略

- `roomote.custom`: 在 `gitlab.branch` 字段指定分支
- `gitlab.jira.fix`: 优先使用 `gitlab.branch`，否则使用 `gitlab.defaultBranch`

### 3. 字段简化

- 统一使用 `modelName` 字段指定AI模型
- 移除了重复的 `modelId` 字段，简化API接口
- 运行时仍保持向后兼容性

## 🧪 测试验证

使用提供的测试脚本验证API功能：

```bash
chmod +x apps/roomote/scripts/test-roomote-api.sh
./apps/roomote/scripts/test-roomote-api.sh
```

测试覆盖范围：

- ✅ `roomote.custom` 自定义任务
- ✅ `gitlab.jira.fix` 增强版JIRA任务
- ✅ 字段简化验证

## 📈 使用流程

### 插件端流程

1. 用户选择任务类型（自定义指令 或 JIRA工单）
2. 用户配置执行参数（分支、模式、自定义指令）
3. 插件解析profile配置，获取apiProvider和modelName
4. 插件构建标准化payload并提交到Roomote API
5. 监控任务执行状态和结果

### 后端处理流程

1. 接收并验证请求payload
2. 创建数据库任务记录
3. 将任务加入处理队列
4. 工作进程执行具体任务
5. 返回任务执行结果

## 🔧 技术实现细节

### 类型简化

- 移除了重复的 `modelId` 字段，统一使用 `modelName`
- 简化API接口定义，减少开发者认知负担
- 运行时保持向后兼容性

### 配置优先级

1. 分支选择: `gitlab.branch` > `gitlab.defaultBranch`
2. 模型配置: `modelName` > 默认值
3. 提供商: `apiProvider` > 默认值

### 任务执行增强

- 自定义指令与JIRA工单内容智能融合
- 支持多种执行模式的提示词优化
- 完整的Git工作流支持（分支、提交、MR创建）

---

_最后更新: 2024年_ | _版本: v2.0_

## 📋 故障排除

### 常见问题

1. **任务创建失败 (HTTP 400)**
    - 检查payload格式是否正确
    - 确认必需字段是否缺失

2. **GitLab连接失败**
    - 验证GITLAB_PAT权限
    - 检查仓库访问权限

3. **任务执行超时**
    - 检查网络连接
    - 确认Worker服务运行状态

### 调试步骤

1. 检查API服务状态
2. 验证环境变量配置
3. 查看Worker日志
4. 使用测试脚本验证功能

## 🎉 总结

经过本次改造，Roomote Agent API 现在完全支持湛卢VS插件的需求：

- ✅ **自定义指令支持**: 用户可以输入任意指令执行任务
- ✅ **模式指定**: 支持不同的AI执行模式
- ✅ **智能配置**: 自动解析用户选择的profile配置
- ✅ **JIRA集成**: 可选的JIRA工单集成功能
- ✅ **分支管理**: 支持指定GitLab分支操作

插件端只需要根据用户配置构建正确的API载荷，即可实现完整的远程代理任务执行功能。
