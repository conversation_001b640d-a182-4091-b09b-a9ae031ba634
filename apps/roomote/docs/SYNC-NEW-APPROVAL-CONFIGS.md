# Remote Agent 批准项同步更新文档

## 更新概述

本次更新同步了 zhanlu-vs 插件中新增的批准项配置，确保 Remote Agent 功能与插件保持一致。

### 新增配置项

#### 1. 问题相关配置

- `alwaysAllowFollowupQuestions`: 自动批准后续问题
- `followupAutoApproveTimeoutMs`: 自动选择第一个答案前的等待时间

#### 2. 待办相关配置

- `alwaysAllowUpdateTodoList`: 自动批准更新待办清单

#### 3. 命令执行相关配置

- `commandExecutionTimeout`: 命令执行超时时间(秒)
- `commandTimeoutAllowlist`: 命令超时白名单
- `deniedCommands`: 拒绝的命令前缀列表
- `preventCompletionWithOpenTodos`: 防止在有未完成待办时完成任务

## 技术实现

### 1. 类型定义更新

在 `src/lib/runTask.ts` 中更新了 `ProfileConfig` 接口：

```typescript
interface ProfileConfig {
	// 新增的批准项配置
	alwaysAllowFollowupQuestions?: boolean
	followupAutoApproveTimeoutMs?: number
	alwaysAllowUpdateTodoList?: boolean
	// 命令执行相关配置
	commandExecutionTimeout?: number
	commandTimeoutAllowlist?: string[]
	deniedCommands?: string[]
	preventCompletionWithOpenTodos?: boolean
	// ... 其他配置
}
```

### 2. 配置合并逻辑

在 `mergeConfigurations` 函数中添加了新配置项的处理：

```typescript
function mergeConfigurations(baseConfig: any, profileConfig: ProfileConfig): any {
	return {
		// ... 现有配置

		// 新增的批准项配置 - 问题和待办
		alwaysAllowFollowupQuestions:
			baseConfig.alwaysAllowFollowupQuestions !== undefined ? baseConfig.alwaysAllowFollowupQuestions : true,
		followupAutoApproveTimeoutMs:
			baseConfig.followupAutoApproveTimeoutMs !== undefined ? baseConfig.followupAutoApproveTimeoutMs : 0,
		alwaysAllowUpdateTodoList:
			baseConfig.alwaysAllowUpdateTodoList !== undefined ? baseConfig.alwaysAllowUpdateTodoList : true,

		// 命令执行权限
		deniedCommands: baseConfig.deniedCommands || [],
		commandExecutionTimeout:
			baseConfig.commandExecutionTimeout !== undefined ? baseConfig.commandExecutionTimeout : 20,
		commandTimeoutAllowlist: baseConfig.commandTimeoutAllowlist || [],
		preventCompletionWithOpenTodos:
			baseConfig.preventCompletionWithOpenTodos !== undefined ? baseConfig.preventCompletionWithOpenTodos : false,
	}
}
```

### 3. 日志增强

添加了新配置项的日志输出，便于调试和监控：

```typescript
logger.info(
	`[runTask] Final configuration alwaysAllowFollowupQuestions: ${finalConfiguration.alwaysAllowFollowupQuestions}`,
)
logger.info(`[runTask] Final configuration alwaysAllowUpdateTodoList: ${finalConfiguration.alwaysAllowUpdateTodoList}`)
logger.info(`[runTask] Final configuration deniedCommands: ${JSON.stringify(finalConfiguration.deniedCommands)}`)
logger.info(`[runTask] Final configuration commandExecutionTimeout: ${finalConfiguration.commandExecutionTimeout}`)
logger.info(
	`[runTask] Final configuration preventCompletionWithOpenTodos: ${finalConfiguration.preventCompletionWithOpenTodos}`,
)
```

## 默认配置值

为确保 Remote Agent 能够自动化执行，设置了以下默认值：

| 配置项                           | 默认值  | 说明                             |
| -------------------------------- | ------- | -------------------------------- |
| `alwaysAllowFollowupQuestions`   | `true`  | 自动批准问题，确保流畅执行       |
| `followupAutoApproveTimeoutMs`   | `0`     | 立即自动选择第一个答案           |
| `alwaysAllowUpdateTodoList`      | `true`  | 自动批准待办更新                 |
| `commandExecutionTimeout`        | `20`    | 20秒命令执行超时                 |
| `commandTimeoutAllowlist`        | `[]`    | 空白名单，所有命令都受超时限制   |
| `deniedCommands`                 | `[]`    | 空拒绝列表，允许执行所有命令     |
| `preventCompletionWithOpenTodos` | `false` | 不阻止任务完成，即使有未完成待办 |

## 配置导入支持

Remote Agent 现在完全支持从 zhanlu-vs 插件导出的配置文件，包括：

1. **Profile 配置**: 通过 `profileConfiguration` 参数传递完整的插件配置
2. **全局设置**: 自动合并全局设置和 profile 特定设置
3. **环境变量保护**: 重要的环境变量（如 API Keys）不会被覆盖

### 配置优先级

1. 环境变量（最高优先级）
2. Profile 配置
3. 全局默认设置
4. EVALS_SETTINGS 默认值（最低优先级）

## 兼容性

### 向后兼容

- 所有现有的 Remote Agent 任务都能正常运行
- 未提供新配置项时使用默认值
- 不会影响现有的 API 接口

### 插件版本要求

- 支持 zhanlu-vs 插件 v2.2.2+ 版本的配置导出格式
- 兼容 profile 配置和全局设置的合并逻辑

## 使用示例

### GitLab + JIRA 任务示例

```json
{
	"type": "gitlab.jira.fix",
	"payload": {
		"jira": {
			"ticket": "PROJ-123",
			"summary": "修复用户认证问题"
		},
		"gitlab": {
			"repo": "group/project",
			"repoName": "project"
		},
		"profileConfiguration": "{\"providerProfiles\":{\"currentApiConfigName\":\"prod-profile\",\"apiConfigs\":{\"prod-profile\":{\"apiProvider\":\"zhanlu\",\"alwaysAllowFollowupQuestions\":true,\"alwaysAllowUpdateTodoList\":true,\"commandExecutionTimeout\":30,\"deniedCommands\":[\"rm -rf\",\"sudo\"]}}},\"globalSettings\":{\"preventCompletionWithOpenTodos\":false}}"
	}
}
```

### GitHub Issue 任务示例

```json
{
	"type": "github.issue.fix",
	"payload": {
		"repo": "owner/repo",
		"issue": 123,
		"title": "修复登录问题",
		"profileConfiguration": "{\"providerProfiles\":{\"currentApiConfigName\":\"dev-profile\",\"apiConfigs\":{\"dev-profile\":{\"alwaysAllowFollowupQuestions\":false,\"followupAutoApproveTimeoutMs\":10000,\"alwaysAllowUpdateTodoList\":true}}}}"
	}
}
```

## 故障排除

### 常见问题

1. **配置未生效**: 检查 `profileConfiguration` 格式是否正确
2. **命令被意外拒绝**: 查看 `deniedCommands` 配置
3. **任务超时**: 调整 `commandExecutionTimeout` 设置

### 调试方法

1. **查看配置日志**:

    ```bash
    docker compose logs worker | grep "Final configuration"
    ```

2. **监控队列状态**:
   访问 http://localhost:3002/admin/queues

3. **检查任务执行**:
    ```bash
    curl http://localhost:3001/api/jobs/{job_id}
    ```

## 更新历史

- **v1.0.0**: 初始实现新批准项配置同步
- 同步了 zhanlu-vs 插件中的"问题"和"待办"批准项
- 添加了命令执行相关的配置项
- 完善了配置合并和日志输出

## 相关文档

- [Remote Agent README](../README.md)
- [动态代码库支持](./DYNAMIC_REPOSITORY_SUPPORT.md)
- [GitLab + JIRA 工作流](./README-********************.md)
