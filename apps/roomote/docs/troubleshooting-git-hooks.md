# Git Hooks 问题排查指南

## 问题描述

在 Roomote Worker 环境中，AI 执行 `git commit` 命令时会卡住，表现为：

1. 命令权限检查通过（`allowedCommands: ["*"]`, `alwaysAllowExecute: true`）
2. 命令开始执行，但长时间无响应
3. 日志中不断出现空的 `command_output` 消息
4. Worker 进程看起来正常，但任务无法完成

## 根本原因

问题不在于命令执行权限，而是 **husky pre-commit hooks 失败**：

### 执行流程

1. ✅ AI 发起 `git commit -m "..."`
2. ✅ 权限检查通过
3. ✅ 命令开始执行
4. ❌ **卡在这里**：husky pre-commit hook 运行失败
5. ❌ Hook 尝试执行 `npm run generate-types`，需要 `tsx` 但找不到
6. ❌ Hook 失败，git commit 进程挂起等待 hook 完成
7. ❌ Worker 持续等待命令输出，创建空的 `command_output` 消息

### 错误信息

```bash
> zhanlu@2.4.1 generate-types
> tsx scripts/generate-types.mts

sh: 1: tsx: not found
husky - pre-commit script failed (code 127)
husky - command not found in PATH=node_modules/.bin:/pnpm:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
```

## 解决方案

### 方案1：全局禁用 Git Hooks（推荐）

在 `Dockerfile.worker` 中添加全局配置：

```dockerfile
# Setup Git credentials and configuration for dynamic repository access
RUN mkdir -p /roo/repos \
  && git config --global user.email "<EMAIL>" \
  && git config --global user.name "wangdepeng" \
  && git config --global credential.helper store \
  && git config --global core.hooksPath /dev/null \
  && echo "https://oauth2:${GH_TOKEN}@github.com" > ~/.git-credentials
```

这会将 Git hooks 路径设置为 `/dev/null`，有效禁用所有 hooks。

### 方案2：使用 --no-verify 标志

如果需要在特定情况下跳过 hooks，可以修改命令：

```bash
git commit --no-verify -m "commit message"
```

### 方案3：安装缺失的依赖

在 Dockerfile 中确保安装所有开发依赖：

```dockerfile
# 安装完整的开发依赖
RUN cd /roo && pnpm install --frozen-lockfile
```

但这会增加镜像大小和构建时间。

## 验证修复

### 测试命令执行

```bash
# 进入容器
docker exec -it roomote-worker-container bash

# 测试 git commit
cd /roo/repos/ai/zhanlu-vs
git add .
git commit -m "Test commit"
```

### 检查 Git 配置

```bash
git config --global --list | grep hooks
# 应该显示: core.hookspath=/dev/null
```

## 预防措施

1. **环境隔离**：Roomote Worker 环境应该与开发环境分离，不需要运行开发时的 hooks
2. **依赖管理**：如果必须运行 hooks，确保所有依赖都已安装
3. **监控日志**：定期检查 worker 日志，及时发现类似问题

## 相关文件

- `apps/roomote/Dockerfile.worker` - Worker 容器配置
- `.husky/pre-commit` - Pre-commit hook 脚本
- `apps/roomote/src/lib/runTask.ts` - 任务执行逻辑
- `src/core/tools/executeCommandTool.ts` - 命令执行工具

## 调试技巧

### 查看正在运行的进程

```bash
docker exec roomote-worker ps aux | grep git
```

### 检查 Git hooks 状态

```bash
docker exec roomote-worker bash -c "cd /roo/repos/ai/zhanlu-vs && git config --list | grep hook"
```

### 手动测试 commit

```bash
docker exec roomote-worker bash -c "cd /roo/repos/ai/zhanlu-vs && timeout 10 git commit -m 'test'"
```

如果超时，说明 hooks 有问题。

## 深度分析：为什么--no-verify不总是有效

经过深入分析发现，问题不仅仅是hooks失败，还涉及VS Code shell integration的处理机制：

### VS Code Shell Integration问题
1. **事件等待**: `TerminalProcess.run()` 中的 `await shellExecutionComplete` 等待命令结束事件
2. **标记检测**: 依赖shell integration标记（`]633;C` 和 `]633;D`）检测命令状态
3. **Hook干扰**: Pre-commit hook输出可能干扰标记检测，导致结束事件未触发
4. **死锁状态**: 即使hooks立即失败，shell integration仍可能卡住

### 解决方案升级
除了禁用hooks，还需要：

1. **使用Execa执行器**:
```typescript
// 在runTask.ts中设置
terminalShellIntegrationDisabled: true,
```

2. **优化命令执行**:
- 分别执行git add和git commit
- 避免使用&&组合命令

## 完整修复方案

参考新文档：
- `apps/roomote/docs/GIT_HOOKS_ISSUE_ANALYSIS_AND_FIX.md` - 详细分析和修复
- `apps/roomote/docs/TROUBLESHOOTING_GIT_COMMANDS.md` - 故障排查指南

## 总结

这个问题的关键在于理解 Git hooks 和 VS Code shell integration 的交互机制。通过多层防护：

1. **Docker层面**禁用hooks消除根本原因
2. **代码层面**使用execa绕过shell integration问题
3. **提示层面**优化命令执行方式

确保 Roomote Worker 环境中的 Git 操作稳定可靠，同时保持开发环境中 hooks 的正常功能。
