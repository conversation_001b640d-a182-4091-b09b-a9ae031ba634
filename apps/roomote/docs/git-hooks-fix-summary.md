# Remote Agent Git Commit 卡住问题修复总结

## 问题描述
Remote Agent worker执行git commit命令时卡住，具体表现为：
- AI执行 `git add README.md && git commit --no-verify -m "..."` 后长时间无响应
- 日志显示空的 `command_output` 消息
- 任务无法完成

## 根本原因
1. **Git hooks失败**: Worker容器缺少 `tsx` 依赖，pre-commit hook执行 `npm run generate-types` 失败
2. **VS Code shell integration卡住**: Hook失败时，VS Code无法正确检测命令结束，导致 `await shellExecutionComplete` 永远等待
3. **--no-verify限制**: 在VS Code shell integration环境下，`--no-verify` 标志可能不完全生效

## 解决方案

### 1. Docker配置修改
**文件**: `apps/roomote/Dockerfile.worker`
```dockerfile
# 添加git hooks禁用配置
git config --global core.hooksPath /dev/null
```

### 2. 代码配置修改  
**文件**: `apps/roomote/src/lib/runTask.ts`
```typescript
// 强制使用execa而不是VS Code shell integration
let finalConfiguration = Object.assign({}, EVALS_SETTINGS, {
    openRouterApiKey: process.env.OPENROUTER_API_KEY,
    terminalShellIntegrationDisabled: true,
})

// 在配置合并函数中也确保设置
function mergeConfigurations(baseConfig: any, profileConfig: ProfileConfig): any {
    return {
        // ... 其他配置
        terminalShellIntegrationDisabled: true,
    }
}
```

### 3. AI提示优化
**文件**: `apps/roomote/src/lib/jobs/fixGitLabJiraIssue.ts` 和 `roomoteCustomTask.ts`
```typescript
// 要求分别执行命令，避免组合命令
3. Make your changes and commit (execute these commands separately):
   git add .
   git commit --no-verify -m "..."

IMPORTANT: Execute git add and git commit as separate commands, do NOT combine them with &&
```

## 技术原理
- **VS Code Shell Integration**: 依赖事件监听和标记检测来判断命令状态，hooks失败可能干扰检测
- **Execa执行器**: 直接执行命令并获取退出码，避免shell integration的复杂逻辑
- **环境隔离**: Remote Agent是自动化环境，不需要开发时的git hooks

## 验证结果
使用测试脚本 `apps/roomote/scripts/test-git-hooks-fix.sh` 验证：
- ✅ Git commit without hooks: 成功
- ✅ Git commit with --no-verify: 成功  
- ✅ Combined git commands: 成功

## 影响范围
- **Remote Agent**: 修复git commit卡住问题，提升任务执行稳定性
- **开发环境**: 无影响，git hooks在开发环境中正常工作
- **性能**: 略有提升，避免了不必要的hook执行

## 相关文件
- `apps/roomote/Dockerfile.worker` - Docker配置
- `apps/roomote/src/lib/runTask.ts` - 核心执行逻辑  
- `apps/roomote/src/lib/jobs/fixGitLabJiraIssue.ts` - GitLab任务提示
- `apps/roomote/src/lib/jobs/roomoteCustomTask.ts` - 自定义任务提示
- `apps/roomote/scripts/test-git-hooks-fix.sh` - 测试脚本

## 总结
通过三层防护（Docker禁用hooks + 代码强制使用execa + AI提示优化），彻底解决了Remote Agent git commit卡住的问题，确保了自动化任务的稳定执行。
