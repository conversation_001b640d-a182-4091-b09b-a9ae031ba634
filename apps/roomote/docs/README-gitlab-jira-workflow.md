# GitLab + JIRA 工作流自动化脚本使用指南

## 📋 概述

GitLab + JIRA 工作流自动化脚本是基于现有GitHub工作流系统开发的扩展功能，专门用于集成JIRA工单管理和GitLab代码仓库，实现从JIRA工单到GitLab Merge Request的全自动化流程。

## 🎯 核心功能

### ✅ 已实现功能

1. **JIRA REST API v2 集成** - 完整支持JIRA工单信息获取
2. **GitLab API 集成** - 支持仓库验证和基本信息获取
3. **参数化配置** - 灵活的命令行参数和环境变量配置
4. **多AI模型支持** - 支持zhanlu、openrouter、anthropic等多种API提供商
5. **完整错误处理** - 详细的错误信息和调试支持
6. **类型安全** - 完整的TypeScript类型定义和验证

### 🔄 计划增强功能

1. 自动创建GitLab分支和Merge Request
2. JIRA工单状态自动更新
3. Slack/邮件通知集成
4. 任务进度追踪界面

## 🛠️ 安装与配置

### 1. 环境依赖

确保已安装以下工具：

```bash
# 必需工具
curl      # HTTP客户端
jq        # JSON处理工具
node      # Node.js运行时 (v18+)
```

### 2. 项目依赖

GitLab + JIRA工作流依赖以下npm包：

```bash
# 已自动安装的依赖
jira.js           # JIRA REST API客户端
@gitbeaker/rest   # GitLab API客户端
```

### 3. 环境配置

环境变量已经集成到 `.env` 文件中，请确保以下配置正确：

```bash
# JIRA 系统配置
JIRA_URL=http://jira.cmss.com
JIRA_USERNAME=your_username
JIRA_PASSWORD=your_password

# GitLab 系统配置
GITLAB_URL=http://gitlab.cmss.com
GITLAB_PAT=your_personal_access_token
```

**注意**: 所有环境变量现已统一在 `.env` 文件中管理，不再需要单独的配置文件。

#### GitLab Personal Access Token 权限要求

创建GitLab PAT时，需要以下权限：

- `api` - 完整API访问
- `read_repository` - 读取仓库
- `write_repository` - 写入仓库

## 📚 使用方法

### 基础语法

```bash
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket <JIRA编号> \
  --gitlab-repo <仓库路径> \
  [选项]
```

### 参数说明

#### 必需参数

| 参数            | 说明           | 示例                     |
| --------------- | -------------- | ------------------------ |
| `--jira-ticket` | JIRA工单编号   | `YDYCMKK-850`            |
| `--gitlab-repo` | GitLab仓库路径 | `paas-aipt/ai/zhanlu-vs` |

#### 可选参数

| 参数             | 说明         | 默认值            |
| ---------------- | ------------ | ----------------- |
| `--api-provider` | AI服务提供商 | `zhanlu`          |
| `--model-id`     | AI模型标识符 | `zhanluAI`        |
| `--type`         | 任务类型     | `gitlab.jira.fix` |
| `--debug`        | 启用调试模式 | `false`           |
| `--help`         | 显示帮助信息 | -                 |

### 使用示例

#### 1. 基础用法

```bash
# 最简单的使用方式
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket YDYCMKK-850 \
  --gitlab-repo paas-aipt/ai/zhanlu-vs
```

#### 2. 指定AI模型

```bash
# 使用OpenRouter和GPT-4模型
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket PROJ-123 \
  --gitlab-repo group/project \
  --api-provider openrouter \
  --model-id gpt-4-turbo
```

#### 3. 调试模式

```bash
# 启用详细日志输出
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket BUG-456 \
  --gitlab-repo team/webapp \
  --debug
```

#### 4. 自定义任务类型

```bash
# 使用增强型任务类型
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket ENH-789 \
  --gitlab-repo dev/frontend \
  --type gitlab.jira.enhancement
```

## 🔧 技术架构

### 系统组件

```
┌─────────────────┐    ┌─────────────────┐
│   JIRA API v2   │    │   GitLab API    │
│  (工单管理)     │    │  (代码仓库)     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│          Bash 主脚本                    │
│  • 参数解析     • 环境验证              │
│  • 流程控制     • 错误处理              │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│        Node.js 辅助脚本                 │
│  • JIRA集成     • GitLab集成            │
│  • JSON处理     • 错误处理              │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│          Roomote API                    │
│  • 任务入队     • 状态管理              │
│  • 类型验证     • 结果跟踪              │
└─────────────────────────────────────────┘
```

### 数据流

1. **参数验证** → 验证命令行参数和环境变量
2. **JIRA集成** → 获取工单详细信息
3. **GitLab验证** → 确认仓库访问权限
4. **载荷构建** → 组织任务数据结构
5. **任务入队** → 提交到Roomote系统
6. **AI处理** → 自动分析和代码修复
7. **MR创建** → 自动创建Merge Request

### 类型定义

```typescript
interface GitLabJiraPayload {
	jira: {
		ticket: string
		summary: string
		description: string
		priority: string
		assignee: string
		status: string
	}
	gitlab: {
		repo: string
		repoName: string
		defaultBranch: string
		webUrl: string
	}
	apiProvider: string
	modelId: string
}
```

## 🚨 故障排除

### 常见问题

#### 1. JIRA连接失败

**错误信息：**

```
❌ JIRA API请求失败 (HTTP 401)
可能原因: 认证失败，请检查JIRA_USERNAME和JIRA_PASSWORD
```

**解决方案：**

- 验证JIRA用户名和密码
- 确认JIRA_URL地址正确
- 检查网络连接

#### 2. GitLab权限不足

**错误信息：**

```
❌ GitLab API请求失败 (HTTP 403)
可能原因: 仓库权限不足或PAT权限不够
```

**解决方案：**

- 检查GitLab PAT权限设置
- 确认对目标仓库有访问权限
- 验证PAT未过期

#### 3. 环境变量缺失

**错误信息：**

```
❌ 缺少必需的环境变量:
  - JIRA_USERNAME
  - GITLAB_PAT
```

**解决方案：**

```bash
# 检查.env文件中的环境变量配置
cat .env | grep -E "(JIRA|GITLAB)"

# 或者手动导出变量
export JIRA_USERNAME="your_username"
export GITLAB_PAT="your_token"
```

#### 4. 依赖工具缺失

**错误信息：**

```
❌ 缺少必需工具:
  - jq
  - curl
```

**解决方案：**

```bash
# Ubuntu/Debian
sudo apt update && sudo apt install jq curl

# macOS
brew install jq

# 验证安装
jq --version
curl --version
```

### 调试技巧

#### 1. 启用调试模式

```bash
./scripts/enqueue-gitlab-repo-job.sh \
  --jira-ticket TICKET-123 \
  --gitlab-repo group/repo \
  --debug
```

#### 2. 分步验证

```bash
# 1. 测试JIRA连接
node scripts/jira-helper.mjs get-issue TICKET-123

# 2. 测试GitLab连接
curl -H "Authorization: Bearer $GITLAB_PAT" \
  "$GITLAB_URL/api/v4/projects/group%2Frepo"

# 3. 测试Roomote API
curl -X GET http://localhost:3001/api/health
```

#### 3. 日志查看

```bash
# 查看Roomote服务日志
docker compose logs -f api worker

# 查看任务状态
curl http://localhost:3001/api/jobs/{job_id}

# 监控队列
访问 http://localhost:3002/admin/queues
```

## 📈 监控与管理

### 任务状态追踪

| 状态         | 说明                 |
| ------------ | -------------------- |
| `pending`    | 任务已创建，等待处理 |
| `processing` | AI代理正在工作       |
| `completed`  | 任务成功完成         |
| `failed`     | 任务执行失败         |

### 监控界面

- **队列监控**: http://localhost:3002/admin/queues
- **API健康检查**: http://localhost:3001/api/health
- **任务详情**: `curl http://localhost:3001/api/jobs/{job_id}`

## 🤝 贡献与反馈

### 已知限制

1. 当前版本仅支持基础的JIRA工单获取
2. GitLab集成尚未完全实现MR自动创建
3. 缺少JIRA状态自动更新功能

### 反馈渠道

如遇到问题或有改进建议，请通过以下方式反馈：

1. 项目Issue系统
2. 内部开发团队
3. 技术文档更新请求

### 版本历史

- **v1.0.0** - 初始版本，基础GitLab + JIRA集成
- **v1.1.0** - 计划版本，完整MR创建功能
- **v1.2.0** - 计划版本，JIRA状态同步功能
