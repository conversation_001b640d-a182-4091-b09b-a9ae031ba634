# 🚀 动态代码库支持 - Remote Agent 优化方案

## 📋 概述

Roomote (Remote Agent) 现已支持**完全动态的代码库管理**，允许您对任意GitHub仓库执行自动化issue修复，而不再局限于固定的代码库。这次重大升级为remote-agent带来了真正的多仓库支持能力。

## ✨ 新功能特性

### 🎯 核心改进

- **✅ 动态仓库克隆** - 自动克隆任意指定的GitHub仓库
- **✅ 分支精确控制** - 支持指定任意分支 (main, develop, feature/xxx等)
- **✅ 智能工作空间管理** - 动态创建和管理工作目录
- **✅ 自动依赖安装** - 智能检测并安装项目依赖 (npm/yarn/pnpm)
- **✅ 仓库验证机制** - 确保克隆正确的仓库版本和分支
- **✅ 增量更新支持** - 复用已存在的仓库并更新到最新版本
- **✅ 智能分支切换** - 自动处理分支创建和切换逻辑
- **✅ 多包管理器支持** - 自动检测并使用合适的包管理器

### 🔧 技术改进

- **移除硬编码限制** - 不再依赖预置的固定代码库
- **优化Docker镜像** - 减少镜像大小，提高启动速度
- **增强错误处理** - 更好的错误提示和恢复机制
- **改进日志记录** - 详细的操作日志便于调试

## 🏗️ 架构变更

### 原有架构（硬编码）

```
Worker Container
├── 预置 /roo/repos/Roo-Code/
├── 固定工作目录
└── 单一仓库支持
```

### 新架构（动态）

```
Worker Container
├── 动态 /roo/repos/{repo-name}/
├── 自动克隆任意仓库
├── 智能依赖管理
└── 多仓库并行支持
```

## 🚀 使用方法

### 基础用法

```bash
# 使用新的动态脚本 (现已支持分支指定)
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh <issue_number> <repo> [branch] [api_provider] [model_name]
```

### 实际示例

```bash
# 修复 Microsoft VSCode 的issue (使用默认分支)
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 123 microsoft/vscode

# 修复特定分支的问题
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 123 microsoft/vscode insiders

# 修复feature分支
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 456 yourname/project feature/new-ui

# 修复develop分支并指定AI模型
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 456 yourname/project develop openrouter gpt-4-turbo

# 修复企业项目的release分支
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 789 company/internal release/v2.0 anthropic claude-3-sonnet

# 修复hotfix分支
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 101 youruser/repo hotfix/critical-bug
```

### 🌿 分支支持详情

新增的分支功能支持多种Git工作流：

```bash
# 主要分支类型支持
main/master     # 主分支 (默认)
develop         # 开发分支
staging         # 预发布分支

# Feature分支
feature/xxx     # 功能开发分支
feature/new-ui  # UI功能分支
feature/api-v2  # API版本2功能分支

# 修复分支
hotfix/xxx      # 热修复分支
bugfix/xxx      # 一般bug修复分支

# 发布分支
release/xxx     # 发布准备分支
release/v2.0    # 版本发布分支
```

**分支验证逻辑：**

- ✅ 自动验证分支是否存在于远程仓库
- ✅ 如果分支不存在，自动使用仓库默认分支
- ✅ 智能处理本地分支和远程分支的创建与切换
- ✅ 支持从指定分支创建Pull Request

### 兼容性支持

原有脚本仍然可用，并且现在支持动态仓库：

```bash
# 原有脚本现在也支持动态仓库
./apps/roomote/scripts/enqueue-github-issue-job.sh 123 microsoft/vscode
```

## 🔧 技术实现详情

### 核心函数

#### 1. `getRepoInfoFromPayload()`

从job payload中提取仓库信息，支持多种格式：

```typescript
interface RepoInfo {
	owner: string // 仓库所有者
	name: string // 仓库名称
	fullName: string // 完整名称 (owner/name)
	branch?: string // 分支名称 (可选，默认: main)
}

// 支持的分支格式示例：
// "main", "develop", "feature/new-ui", "hotfix/critical-bug", "release/v2.0"
```

#### 2. `setupWorkspace()`

动态设置工作空间的核心函数：

- 🔍 检查仓库是否已存在
- 🔄 验证仓库正确性
- 📥 克隆或更新仓库
- 📦 安装项目依赖

#### 3. `verifyRepository()`

验证本地仓库是否与期望仓库匹配：

```bash
# 支持多种URL格式验证
https://github.com/owner/repo.git
https://github.com/owner/repo
**************:owner/repo.git
```

#### 4. `cloneRepository()`

使用GitHub CLI克隆仓库，享受现有认证：

```bash
gh repo clone owner/repo /path/to/workspace
gh repo set-default owner/repo
```

#### 5. `installDependencies()`

智能依赖管理，自动检测包管理器：

```bash
# 自动检测并使用
pnpm install    # 如果存在 pnpm-lock.yaml
yarn install    # 如果存在 yarn.lock
npm install     # 默认情况
```

### Docker镜像优化

#### 新的Dockerfile.worker

- ❌ 移除预置仓库克隆
- ✅ 保留Git认证配置
- ✅ 创建通用工作目录结构
- ✅ 优化构建层级，减少镜像大小

## 📊 工作流程

### 1. 任务创建阶段

```
用户脚本 → API端点 → 验证payload → 入队
```

### 2. 任务执行阶段

```
Worker启动 → 解析仓库信息 → 设置工作空间 → 启动VS Code
```

### 3. 工作空间设置

```
创建目录 → 验证仓库 → 克隆/更新 → 安装依赖 → 就绪
```

### 4. AI代理工作

```
分析issue → 编写代码 → 测试验证 → 创建PR → 完成
```

## 🛠️ 配置说明

### 环境变量

```bash
# GitHub认证 (必需)
GH_TOKEN=ghp_xxxxxxxxxxxx

# 其他AI提供商API密钥 (可选)
OPENROUTER_API_KEY=sk-or-xxxxxxxxxxxxx
ZHANLU_ACCESS_KEY=your_zhanlu_key
ZHANLU_SECRET_KEY=your_zhanlu_secret
```

### 目录结构

```
/roo/
├── repos/                    # 动态仓库目录
│   ├── repo1/               # 第一个仓库
│   ├── repo2/               # 第二个仓库
│   └── ...                  # 更多仓库
├── .vscode/                 # VS Code配置
└── logs/                    # 日志文件
```

## 🔍 监控和调试

### 监控面板

```bash
# 队列监控
http://localhost:3002/admin/queues

# 任务状态查询
curl http://localhost:3001/api/jobs/{jobId}
```

### 日志查看

```bash
# 实时日志
docker compose logs -f worker

# 特定容器日志
docker logs roomote-worker-{timestamp}
```

### 调试信息

系统会记录详细的操作日志：

```
[setupWorkspace] Workspace already exists: /roo/repos/microsoft-vscode
[verifyRepository] Repository mismatch. Expected: microsoft/vscode, Found: https://github.com/old/repo.git
[cloneRepository] Cloning microsoft/vscode to /roo/repos/microsoft-vscode
[installDependencies] Installing dependencies using pnpm
```

## 🚨 注意事项

### 权限要求

- 🔑 需要对目标仓库有读取权限
- 🔑 需要有创建分支和PR的权限
- 🔑 GitHub CLI必须正确配置认证

### 性能考虑

- 💾 每个仓库会占用磁盘空间
- ⏱️ 首次克隆大仓库可能需要较长时间
- 🔄 系统会智能复用已克隆的仓库

### 安全考虑

- 🛡️ 只支持GitHub公开仓库和有权限的私有仓库
- 🛡️ 所有操作都通过GitHub CLI的认证机制
- 🛡️ Worker容器使用隔离的环境

## 🆕 迁移指南

### 从旧版本升级

1. **重新构建Worker镜像**

```bash
# 构建新的worker镜像
./scripts/build.sh worker
```

2. **使用新脚本**

```bash
# 使用增强版脚本
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 123 your/repo
```

3. **验证功能**

```bash
# 测试不同仓库
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 1 facebook/react
./apps/roomote/scripts/enqueue-dynamic-repo-job.sh 2 microsoft/typescript
```

### 兼容性

- ✅ 原有API接口完全兼容
- ✅ 原有脚本继续工作
- ✅ 配置文件无需修改
- ✅ 数据库schema保持不变

## 📈 性能优化建议

### 1. 仓库缓存策略

```bash
# 定期清理不用的仓库
find /roo/repos -type d -mtime +7 -exec rm -rf {} \;
```

### 2. 依赖缓存

```bash
# 使用全局包管理器缓存
export PNPM_HOME=/roo/.pnpm
export NPM_CONFIG_CACHE=/roo/.npm
```

### 3. 并发控制

```bash
# 限制同时处理的仓库数量
MAX_CONCURRENT_REPOS=3
```

## 🔮 未来规划

### 短期目标

- 🎯 支持Git子模块
- 🎯 增加仓库大小限制
- 🎯 支持私有GitLab仓库
- 🎯 添加仓库预热功能

### 长期目标

- 🚀 支持多Git平台 (GitLab, Bitbucket)
- 🚀 添加代码质量检查
- 🚀 集成CI/CD流水线
- 🚀 支持批量issue处理

## 🤝 贡献指南

欢迎为动态代码库功能做出贡献：

1. 🍴 Fork仓库
2. 🌟 创建功能分支
3. 💻 实现新功能
4. ✅ 添加测试用例
5. 📝 更新文档
6. 🚀 提交PR

## 📞 技术支持

如遇到问题，请：

1. 📋 查看日志文件
2. 🔍 检查GitHub认证
3. 💬 在GitHub Issues中报告问题
4. 📧 联系技术支持团队

---

**🎉 动态代码库支持让remote-agent真正实现了"一键修复任意仓库"的愿景！**
