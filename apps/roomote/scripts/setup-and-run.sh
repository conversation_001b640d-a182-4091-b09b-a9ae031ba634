#!/bin/bash

# GitLab + JIRA 工作流一键启动脚本
# 自动处理环境变量、服务启动和任务执行

set -euo pipefail

# 显示帮助信息
show_help() {
    echo "=========================================="
    echo "🚀 GitLab + JIRA 工作流一键启动脚本"
    echo "=========================================="
    echo ""
    echo "用法:"
    echo "  $0 --jira-ticket <JIRA编号> --gitlab-repo <仓库路径> [选项]"
    echo ""
    echo "功能:"
    echo "  ✅ 自动加载环境变量"
    echo "  ✅ 自动启动必需服务"
    echo "  ✅ 自动运行数据库迁移"
    echo "  ✅ 执行GitLab + JIRA任务"
    echo ""
    echo "示例:"
    echo "  $0 --jira-ticket YDYCMKK-852 --gitlab-repo paas-aipt/ai/zhanlu-vs"
    echo ""
}

# 检查参数
if [ $# -eq 0 ] || [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    show_help
    exit 0
fi

echo "=========================================="
echo "🚀 GitLab + JIRA 工作流一键启动"
echo "=========================================="
echo ""

# 步骤1：加载环境变量
echo "📋 步骤1：加载环境变量..."
if [ -f ".env" ]; then
    # 加载并导出环境变量
    set -a  # 自动导出所有变量
    source .env
    set +a  # 关闭自动导出
    echo "✅ 环境变量加载成功"
else
    echo "❌ 环境配置文件不存在: .env"
    echo "请先复制并配置环境变量文件:"
    echo "  cp .env.example .env"
    exit 1
fi
echo ""

# 步骤2：启动基础服务
echo "📋 步骤2：启动基础服务..."
echo "启动数据库、Redis和监控面板..."
pnpm services:start
sleep 2
echo "✅ 基础服务启动成功"
echo ""

# 步骤3：运行数据库迁移
echo "📋 步骤3：检查数据库..."
if ! pnpm db:migrate; then
    echo "⚠️  数据库迁移失败，可能是连接问题，等待3秒后重试..."
    sleep 3
    pnpm db:migrate
fi
echo "✅ 数据库准备完成"
echo ""

# 步骤4：启动API服务（如果还没运行）
echo "📋 步骤4：检查API服务..."
if ! curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
    echo "启动API服务..."
    pnpm dev &
    API_PID=$!
    echo "等待API服务启动..."
    
    # 等待API服务启动（最多30秒）
    for i in {1..30}; do
        if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
            echo "✅ API服务启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ API服务启动超时"
            exit 1
        fi
        sleep 1
    done
else
    echo "✅ API服务已在运行"
fi
echo ""

# 步骤5：执行GitLab + JIRA任务
echo "📋 步骤5：执行GitLab + JIRA任务..."
echo "参数: $@"
echo ""

./scripts/enqueue-gitlab-repo-job.sh "$@"

echo ""
echo "🎉 任务执行完成！"
echo ""
echo "📈 监控链接:"
echo "  🌐 队列监控: http://localhost:3002/admin/queues"
echo "  ❤️  API健康检查: http://localhost:3001/api/health" 