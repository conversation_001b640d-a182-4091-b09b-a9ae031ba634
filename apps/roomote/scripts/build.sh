#!/bin/bash

# Build script for roomote services.
# This ensures the base image is built before dependent services.

set -e

# Color codes for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to prepare build environment
prepare_build_env() {
    print_info "Preparing build environment..."
    sudo chmod -R 755 .docker/ 2>/dev/null || true

    # 获取脚本目录
    SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
    # 推算项目根目录（根据实际目录结构调整）
    REPO_ROOT="$SCRIPT_DIR/../../../"

    # 在根目录下执行 pnpm vsix
    print_info "Running pnpm vsix in repository root..."
    (cd "$REPO_ROOT" && pnpm vsix)
}

# Function to build a single service
build_service() {
    local service=$1
    local skip_base=${2:-false}

    case $service in
        "dashboard"|"api"|"worker"|"controller")
            if [ "$skip_base" = false ]; then
                print_info "Building base image first..."
                prepare_build_env
                docker compose build base
            fi
            print_info "Building $service..."
            docker compose build $service
            ;;
        "base")
            print_info "Building base image..."
            prepare_build_env
            docker compose build base
            ;;
        "db"|"redis")
            print_info "Service '$service' uses pre-built images, no build required."
            return 0
            ;;
        *)
            print_info "Building $service..."
            docker compose build $service
            ;;
    esac

    print_success "$service build completed!"
}

# Function to build all services
build_all() {
    print_info "Starting build all process..."
    print_info "This will rebuild all Docker images in the correct order"

    # Prepare environment once for all builds
    prepare_build_env

    # Define build order - base first, then dependent services
    local services=("base" "dashboard" "api" "worker" "controller")

    print_info "Building services in order: ${services[*]}"

    # Build base image first
    print_info "Step 1/5: Building base image..."
    docker compose build base
    print_success "Base image build completed!"

    # Build dependent services (skip base build for these)
    local step=2
    for service in "dashboard" "api" "worker" "controller"; do
        print_info "Step $step/5: Building $service..."
        docker compose build $service
        print_success "$service build completed!"
        ((step++))
    done

    print_success "All services built successfully!"
    print_info "Services that use pre-built images (db, redis) don't require building."
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <service_name|all>"
    echo ""
    echo "Available options:"
    echo "  all                    - Build all services (base, dashboard, api, worker, controller)"
    echo "  base                   - Build base image only"
    echo "  dashboard              - Build dashboard service (includes base)"
    echo "  api                    - Build API service (includes base)"
    echo "  worker                 - Build worker service (includes base)"
    echo "  controller             - Build controller service (includes base)"
    echo ""
    echo "Note: db and redis services use pre-built images and don't require building"
    echo ""
    echo "Examples:"
    echo "  $0 all                 # Rebuild all services"
    echo "  $0 dashboard           # Build dashboard service"
    echo "  $0 base                # Build base image only"
}

# Main script logic
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

case $1 in
    "all")
        build_all
        ;;
    "base"|"dashboard"|"api"|"worker"|"controller"|"db"|"redis")
        build_service $1
        ;;
    "-h"|"--help"|"help")
        show_usage
        exit 0
        ;;
    *)
        print_error "Unknown service: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac

print_success "Build process completed successfully!"
