#!/bin/bash

# 增强版GitHub Issue Job脚本 - 支持完全动态的代码库和分支
# 此脚本演示了如何使用新的动态代码库功能，包括分支指定

ISSUE_NUMBER="$1"
REPO="$2"
BRANCH="$3"
API_PROVIDER="${4:-zhanlu}"  # 默认使用zhanlu
MODEL_NAME="${5:-zhanluAI}"  # 默认使用zhanluAI模型
BASE_URL="http://localhost:3001"
JOBS_ENDPOINT="$BASE_URL/api/jobs"

if [ -z "$ISSUE_NUMBER" ] || [ -z "$REPO" ]; then
    echo "=========================================="
    echo "🚀 动态代码库 GitHub Issue 修复器"
    echo "=========================================="
    echo ""
    echo "用法: $0 <issue_number> <repo> [branch] [api_provider] [model_name]"
    echo ""
    echo "参数说明："
    echo "  issue_number : GitHub issue编号 (必需)"
    echo "  repo        : 仓库名称，格式为 'owner/repo' (必需)"
    echo "  branch      : 分支名称 (可选，默认: main)"
    echo "  api_provider: API提供商 (可选，默认: zhanlu)"
    echo "  model_name  : 模型名称 (可选，默认: zhanluAI)"
    echo ""
    echo "功能特性："
    echo "  ✅ 支持任意GitHub仓库"
    echo "  ✅ 支持指定分支 (main, develop, feature/xxx等)"
    echo "  ✅ 自动克隆和更新代码库"
    echo "  ✅ 智能依赖管理 (npm/yarn/pnpm)"
    echo "  ✅ 多种AI模型支持"
    echo "  ✅ 自动创建Pull Request"
    echo ""
    echo "使用示例："
    echo "  # 修复开源项目的bug (使用默认分支main)"
    echo "  $0 123 microsoft/vscode"
    echo ""
    echo "  # 修复特定分支的问题"
    echo "  $0 123 microsoft/vscode develop"
    echo ""
    echo "  # 修复feature分支并指定AI模型"
    echo "  $0 456 yourname/project feature/new-ui openrouter gpt-4-turbo"
    echo ""
    echo "  # 修复release分支"
    echo "  $0 789 company/tool release/v2.0 anthropic claude-3-sonnet"
    echo ""
    echo "  # 修复个人项目的特定分支"
    echo "  $0 101 youruser/repo hotfix/critical-bug zhanlu zhanluAI"
    echo ""
    echo "分支命名规范："
    echo "  • main/master - 主分支"
    echo "  • develop - 开发分支"  
    echo "  • feature/xxx - 功能分支"
    echo "  • hotfix/xxx - 热修复分支"
    echo "  • release/xxx - 发布分支"
    echo ""
    echo "注意事项："
    echo "  • 确保已安装并配置GitHub CLI (gh auth login)"
    echo "  • 需要对目标仓库有push权限"
    echo "  • 确保指定的分支在仓库中存在"
    echo "  • 系统会自动处理依赖安装和环境配置"
    echo ""
    exit 1
fi

# 处理分支参数，如果为空则使用默认值
if [ -z "$BRANCH" ] || [ "$BRANCH" = "zhanlu" ] || [ "$BRANCH" = "openrouter" ] || [ "$BRANCH" = "anthropic" ] || [ "$BRANCH" = "openai" ]; then
    # 如果第三个参数是API提供商，需要重新调整参数
    if [ "$BRANCH" = "zhanlu" ] || [ "$BRANCH" = "openrouter" ] || [ "$BRANCH" = "anthropic" ] || [ "$BRANCH" = "openai" ]; then
        API_PROVIDER="$BRANCH"
        MODEL_NAME="${4:-zhanluAI}"
        BRANCH=""
    fi
fi

# 如果没有指定分支，使用默认值
if [ -z "$BRANCH" ]; then
    BRANCH="main"
fi

echo "=========================================="
echo "🚀 启动动态代码库任务"
echo "=========================================="
echo "配置信息:"
echo "  🏷️  仓库: $REPO"
echo "  🌿 分支: $BRANCH"
echo "  🔢 Issue: #$ISSUE_NUMBER"
echo "  🤖 AI提供商: $API_PROVIDER"
echo "  🧠 模型: $MODEL_NAME"
echo "  🌐 服务器: $BASE_URL"
echo ""

# 验证必要工具
echo "🔍 检查必要工具..."

if ! command -v gh &> /dev/null; then
    echo "❌ 错误: 未安装GitHub CLI"
    echo "   请访问: https://cli.github.com/"
    exit 1
fi

if ! gh auth status &> /dev/null; then
    echo "❌ 错误: GitHub CLI未认证"
    echo "   请运行: gh auth login"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo "❌ 错误: 未安装jq"
    echo "   请访问: https://jqlang.github.io/jq/download/"
    exit 1
fi

echo "✅ 所有工具检查通过"
echo ""

# 验证仓库访问权限
echo "🔐 验证仓库访问权限..."
if ! gh repo view $REPO &> /dev/null; then
    echo "❌ 错误: 无法访问仓库 $REPO"
    echo "   请检查仓库名称和访问权限"
    exit 1
fi
echo "✅ 仓库访问权限验证通过"
echo ""

# 验证分支是否存在
echo "🌿 验证分支存在性..."
if ! gh api repos/$REPO/branches/$BRANCH &> /dev/null; then
    echo "⚠️  警告: 分支 '$BRANCH' 在仓库 $REPO 中不存在"
    echo "   将使用默认分支，但建议检查分支名称"
    echo ""
    # 获取默认分支
    DEFAULT_BRANCH=$(gh repo view $REPO --json defaultBranch -q '.defaultBranch')
    if [ -n "$DEFAULT_BRANCH" ]; then
        echo "   仓库的默认分支是: $DEFAULT_BRANCH"
        BRANCH="$DEFAULT_BRANCH"
        echo "   将使用默认分支: $BRANCH"
    fi
else
    echo "✅ 分支 '$BRANCH' 验证通过"
fi
echo ""

# 获取Issue信息
echo "📝 获取Issue #${ISSUE_NUMBER} 信息..."

ISSUE_DATA=$(gh issue view ${ISSUE_NUMBER} --repo ${REPO} --json title,body,labels 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "❌ 错误: 无法获取Issue #${ISSUE_NUMBER}"
    echo "   请检查Issue编号和仓库权限"
    exit 1
fi

TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
LABELS=$(echo "$ISSUE_DATA" | jq -r '[.labels[].name] | @json')

echo "✅ Issue信息获取成功"
echo "   标题: $TITLE"
echo "   标签: $(echo "$LABELS" | jq -r 'join(", ")')"
echo ""

# 构建任务载荷
echo "📦 构建任务载荷..."

JSON_PAYLOAD=$(jq -n \
  --arg type "github.issue.fix" \
  --arg repo "$REPO" \
  --arg branch "$BRANCH" \
  --argjson issue "$ISSUE_NUMBER" \
  --arg title "$TITLE" \
  --arg body "$BODY" \
  --argjson labels "$LABELS" \
  --arg apiProvider "$API_PROVIDER" \
  --arg modelName "$MODEL_NAME" \
  '{
    type: $type,
    payload: {
      repo: $repo,
      branch: $branch,
      issue: $issue,
      title: $title,
      body: $body,
      labels: $labels,
      apiProvider: $apiProvider,
      modelName: $modelName
    }
  }')

echo "✅ 任务载荷构建完成"
echo ""

# 显示任务详情
echo "📋 任务详情预览:"
echo "$JSON_PAYLOAD" | jq '.'
echo ""

# 发送任务
echo "🚀 发送任务到remote-agent..."
echo ""

RESPONSE=$(curl -s -X POST "$JOBS_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d "$JSON_PAYLOAD" \
  -w "HTTP_STATUS:%{http_code}")

HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ 任务成功提交!"
    echo ""
    echo "📊 响应信息:"
    echo "$RESPONSE_BODY" | jq '.'
    
    JOB_ID=$(echo "$RESPONSE_BODY" | jq -r '.jobId')
    ENQUEUED_JOB_ID=$(echo "$RESPONSE_BODY" | jq -r '.enqueuedJobId')
    
    echo ""
    echo "🎯 任务追踪:"
    echo "  📋 Job ID: $JOB_ID"
    echo "  🔗 Queue ID: $ENQUEUED_JOB_ID"
    echo ""
    echo "📈 监控方式:"
    echo "  🌐 监控面板: http://localhost:3002/admin/queues"
    echo "  📋 任务状态: curl $BASE_URL/api/jobs/$JOB_ID"
    echo "  📝 实时日志: docker compose logs -f worker"
    echo ""
    echo "🔮 预期流程:"
    echo "  1. 🤖 AI代理将自动克隆仓库: $REPO"
    echo "  2. 🌿 切换到指定分支: $BRANCH"
    echo "  3. 📊 分析Issue: $TITLE"
    echo "  4. 💻 编写解决方案"
    echo "  5. 🧪 运行测试验证"
    echo "  6. 📝 创建Pull Request (从 $BRANCH 到默认分支)"
    echo "  7. 🔗 自动关联Issue #$ISSUE_NUMBER"
    echo ""
    echo "🎉 任务已启动，请等待AI代理完成工作！"
else
    echo "❌ 任务提交失败 (HTTP $HTTP_STATUS)"
    echo ""
    echo "错误响应:"
    echo "$RESPONSE_BODY" | jq '.' 2>/dev/null || echo "$RESPONSE_BODY"
    exit 1
fi 