#!/bin/bash

# GitLab认证配置脚本
# 在容器运行时配置GitLab CLI和Git认证，避免交互式认证

set -euo pipefail

echo "🔧 配置GitLab认证..."

# 检查必需的环境变量
if [ -z "${GITLAB_PAT:-}" ]; then
    echo "⚠️  GITLAB_PAT环境变量未设置，跳过GitLab认证配置"
    exit 0
fi

if [ -z "${GITLAB_URL:-}" ]; then
    echo "⚠️  GITLAB_URL环境变量未设置，使用默认值"
    GITLAB_URL="http://gitlab.cmss.com"
fi

# 确保配置目录存在
mkdir -p /root/.config/glab-cli

# 从GITLAB_URL提取主机名
GITLAB_HOST=$(echo "$GITLAB_URL" | sed -e 's|^[^/]*//||' -e 's|/.*$||')
GITLAB_PROTOCOL=$(echo "$GITLAB_URL" | sed -e 's|://.*$||')

echo "📍 GitLab主机: $GITLAB_HOST"
echo "📍 协议: $GITLAB_PROTOCOL"

# 配置glab CLI
cat > /root/.config/glab-cli/config.yml << EOF
hosts:
  $GITLAB_HOST:
    api_protocol: $GITLAB_PROTOCOL
    api_host: $GITLAB_HOST
    token: $GITLAB_PAT
    git_protocol: $GITLAB_PROTOCOL
    user: ""
EOF

# 设置正确的权限（glab要求配置文件权限为600）
chmod 600 /root/.config/glab-cli/config.yml

echo "✅ glab认证配置完成"

# 配置Git credentials（运行时更新）
if [ ! -f ~/.git-credentials ]; then
    touch ~/.git-credentials
fi

# 移除可能的重复条目
sed -i "/$GITLAB_HOST/d" ~/.git-credentials

# 添加GitLab认证
echo "$GITLAB_PROTOCOL://oauth2:$GITLAB_PAT@$GITLAB_HOST" >> ~/.git-credentials

echo "✅ Git credentials配置完成"

# 验证配置
echo "🔍 验证GitLab配置..."

# 设置默认GitLab实例（在验证之前设置）
echo "📍 设置默认GitLab主机为: $GITLAB_HOST"
export GITLAB_HOST="$GITLAB_HOST"

# 使用glab配置默认主机
glab config set --global --host="$GITLAB_HOST" git_protocol "$GITLAB_PROTOCOL"
glab config set --global --host="$GITLAB_HOST" api_protocol "$GITLAB_PROTOCOL"

# 测试glab认证
echo "🔍 测试glab连接..."
if timeout 10 glab auth status --hostname="$GITLAB_HOST" >/dev/null 2>&1; then
    echo "✅ glab认证验证成功"
else
    echo "⚠️  glab认证测试超时或失败，但配置文件已生成"
    echo "💡 这可能是网络问题，glab配置应该仍然有效"
fi

# 显示当前配置状态
echo "📊 当前glab配置:"
glab config list --global 2>/dev/null || echo "   配置文件已创建"

echo "🎉 GitLab认证配置完成！" 