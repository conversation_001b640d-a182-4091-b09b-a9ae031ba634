#!/bin/bash

# GitLab认证测试脚本
# 用于验证glab配置是否正确

set -euo pipefail

echo "🧪 测试GitLab认证配置..."

# 检查环境变量
echo "📋 检查环境变量:"
echo "   GITLAB_PAT: ${GITLAB_PAT:+已设置} ${GITLAB_PAT:-未设置}"
echo "   GITLAB_URL: ${GITLAB_URL:-未设置}"

# 检查配置文件
echo "📁 检查配置文件:"
if [ -f /root/.config/glab-cli/config.yml ]; then
    echo "   ✅ glab配置文件存在"
    echo "   📄 配置文件内容:"
    cat /root/.config/glab-cli/config.yml | sed 's/^/      /'
else
    echo "   ❌ glab配置文件不存在"
fi

# 检查Git credentials
echo "🔐 检查Git credentials:"
if [ -f ~/.git-credentials ]; then
    echo "   ✅ Git credentials文件存在"
    # 不显示完整的credentials，只显示主机
    grep -o 'https\?://[^@]*@[^/]*' ~/.git-credentials | sed 's/@.*/@[REDACTED]/' | sed 's/^/      /' || echo "   无GitLab条目"
else
    echo "   ❌ Git credentials文件不存在"
fi

# 测试glab命令
echo "🔧 测试glab命令:"
if command -v glab >/dev/null 2>&1; then
    echo "   ✅ glab命令可用"
    
    # 测试认证状态
    if [ -n "${GITLAB_URL:-}" ]; then
        GITLAB_HOST=$(echo "$GITLAB_URL" | sed -e 's|^[^/]*//||' -e 's|/.*$||')
        echo "   🔍 测试主机: $GITLAB_HOST"
        
        if timeout 5 glab auth status --hostname="$GITLAB_HOST" >/dev/null 2>&1; then
            echo "   ✅ 认证测试成功"
        else
            echo "   ⚠️  认证测试失败（可能是网络问题）"
        fi
    else
        echo "   ⚠️  GITLAB_URL未设置，跳过认证测试"
    fi
else
    echo "   ❌ glab命令不可用"
fi

echo "🎯 测试完成！" 