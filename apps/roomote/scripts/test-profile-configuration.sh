#!/bin/bash

# Remote Agent Profile配置传递测试套件
# 合并了全Provider测试和Profile修复测试

set -e

echo "🧪 Remote Agent Profile配置传递测试套件"
echo "========================================"

# 检查服务状态
echo "📋 检查 Roomote 服务状态..."
if ! docker compose ps | grep -q "Up"; then
    echo "⚠️  Roomote 服务未运行，启动服务..."
    docker compose up -d
    echo "⏳ 等待服务启动..."
    sleep 10
fi

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_provider() {
    local provider_name="$1"
    local profile_config="$2"
    local expected_provider="$3"
    local expected_model="$4"
    
    echo ""
    echo "🔬 测试 $provider_name Provider"
    echo "----------------------------------------"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 创建测试任务
    local task_payload=$(cat <<EOF
{
  "type": "roomote.custom",
  "payload": {
    "customInstructions": "测试 $provider_name 配置传递：请简单回复 Hello from $provider_name!",
    "gitlab": {
      "repo": "test/repo",
      "repoName": "test-repo",
      "defaultBranch": "main", 
      "webUrl": "http://gitlab.example.com/test/repo",
      "branch": "test-branch"
    },
    "mode": "code",
    "apiProvider": "$expected_provider",
    "modelName": "$expected_model",
    "profileConfiguration": "$profile_config"
  }
}
EOF
)
    
    echo "📤 发送 $provider_name 测试任务..."
    local response=$(curl -s -X POST http://localhost:3001/api/jobs \
      -H "Content-Type: application/json" \
      -d "$task_payload")
    
    echo "📥 API 响应: $response"
    
    # 检查响应
    if echo "$response" | jq -e '.success or .jobId or .enqueuedJobId' > /dev/null 2>&1; then
        local job_id=$(echo "$response" | jq -r '.jobId // .enqueuedJobId // "unknown"')
        echo "✅ $provider_name 任务创建成功! Job ID: $job_id"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        
        # 等待一段时间让任务开始执行
        sleep 2
        
        # 检查任务数据中的配置
        echo "🔍 验证任务配置..."
        local task_data=$(docker compose exec redis redis-cli HGET "bull:roomote:roomote.custom-$job_id" "data" 2>/dev/null || echo "")
        if [[ -n "$task_data" && "$task_data" != *"error"* ]]; then
            echo "✅ 任务配置已正确存储"
        else
            echo "⚠️  无法验证任务配置存储"
        fi
        
    else
        echo "❌ $provider_name 任务创建失败!"
        echo "错误详情: $response"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# DeepSeek Provider 测试
DEEPSEEK_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"deepseek-test\",\"apiConfigs\":{\"deepseek-test\":{\"apiProvider\":\"deepseek\",\"deepSeekApiKey\":\"sk-test-deepseek-key\",\"deepSeekBaseUrl\":\"https://api.deepseek.com/v1\",\"apiModelId\":\"deepseek-chat\",\"id\":\"deepseek-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "DeepSeek" "$DEEPSEEK_CONFIG" "deepseek" "deepseek-chat"

# OpenAI Provider 测试
OPENAI_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"openai-test\",\"apiConfigs\":{\"openai-test\":{\"apiProvider\":\"openai\",\"openAiApiKey\":\"sk-test-openai-key\",\"openAiBaseUrl\":\"https://api.openai.com/v1\",\"openAiModelId\":\"gpt-4\",\"id\":\"openai-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "OpenAI" "$OPENAI_CONFIG" "openai" "gpt-4"

# Anthropic Provider 测试
ANTHROPIC_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"anthropic-test\",\"apiConfigs\":{\"anthropic-test\":{\"apiProvider\":\"anthropic\",\"anthropicApiKey\":\"sk-ant-test-key\",\"apiModelId\":\"claude-3-5-sonnet-20241022\",\"id\":\"anthropic-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Anthropic" "$ANTHROPIC_CONFIG" "anthropic" "claude-3-5-sonnet-20241022"

# OpenRouter Provider 测试
OPENROUTER_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"openrouter-test\",\"apiConfigs\":{\"openrouter-test\":{\"apiProvider\":\"openrouter\",\"openRouterApiKey\":\"sk-or-test-key\",\"openRouterModelId\":\"anthropic/claude-3.5-sonnet\",\"id\":\"openrouter-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "OpenRouter" "$OPENROUTER_CONFIG" "openrouter" "anthropic/claude-3.5-sonnet"

# Zhanlu Provider 测试
ZHANLU_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"zhanlu-test\",\"apiConfigs\":{\"zhanlu-test\":{\"apiProvider\":\"zhanlu\",\"zhanluToken\":\"test-zhanlu-token\",\"zhanluModelId\":\"zhanluAI\",\"id\":\"zhanlu-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Zhanlu" "$ZHANLU_CONFIG" "zhanlu" "zhanluAI"

# Gemini Provider 测试
GEMINI_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"gemini-test\",\"apiConfigs\":{\"gemini-test\":{\"apiProvider\":\"gemini\",\"geminiApiKey\":\"test-gemini-key\",\"apiModelId\":\"gemini-1.5-pro\",\"id\":\"gemini-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Gemini" "$GEMINI_CONFIG" "gemini" "gemini-1.5-pro"

# Ollama Provider 测试
OLLAMA_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"ollama-test\",\"apiConfigs\":{\"ollama-test\":{\"apiProvider\":\"ollama\",\"ollamaBaseUrl\":\"http://localhost:11434\",\"ollamaModelId\":\"llama2\",\"id\":\"ollama-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Ollama" "$OLLAMA_CONFIG" "ollama" "llama2"

# Mistral Provider 测试
MISTRAL_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"mistral-test\",\"apiConfigs\":{\"mistral-test\":{\"apiProvider\":\"mistral\",\"mistralApiKey\":\"test-mistral-key\",\"apiModelId\":\"mistral-large-latest\",\"id\":\"mistral-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Mistral" "$MISTRAL_CONFIG" "mistral" "mistral-large-latest"

# xAI Provider 测试
XAI_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"xai-test\",\"apiConfigs\":{\"xai-test\":{\"apiProvider\":\"xai\",\"xaiApiKey\":\"test-xai-key\",\"apiModelId\":\"grok-beta\",\"id\":\"xai-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "xAI" "$XAI_CONFIG" "xai" "grok-beta"

# Groq Provider 测试
GROQ_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"groq-test\",\"apiConfigs\":{\"groq-test\":{\"apiProvider\":\"groq\",\"groqApiKey\":\"test-groq-key\",\"apiModelId\":\"llama-3.1-70b-versatile\",\"id\":\"groq-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "Groq" "$GROQ_CONFIG" "groq" "llama-3.1-70b-versatile"

# 额外的重点测试：DeepSeek和OpenAI Compatible（原test-profile-fix.sh的内容）
echo ""
echo "🔬 重点测试：DeepSeek和OpenAI Compatible配置"
echo "============================================"

# DeepSeek重点测试（使用OpenAI Compatible格式）
echo ""
echo "🎯 DeepSeek重点测试（OpenAI Compatible格式）"
echo "--------------------------------------------"

DEEPSEEK_OPENAI_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"deepseek-openai-test\",\"apiConfigs\":{\"deepseek-openai-test\":{\"apiProvider\":\"openai\",\"openAiBaseUrl\":\"https://api.deepseek.com/v1\",\"openAiApiKey\":\"sk-test-deepseek-key\",\"openAiModelId\":\"deepseek-chat\",\"id\":\"deepseek-openai-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "DeepSeek (OpenAI Compatible)" "$DEEPSEEK_OPENAI_CONFIG" "openai" "deepseek-chat"

# OpenAI标准测试
echo ""
echo "🎯 OpenAI标准测试"
echo "------------------"

OPENAI_STANDARD_CONFIG='{\"providerProfiles\":{\"currentApiConfigName\":\"openai-standard-test\",\"apiConfigs\":{\"openai-standard-test\":{\"apiProvider\":\"openai\",\"openAiBaseUrl\":\"https://api.openai.com/v1\",\"openAiApiKey\":\"sk-test-openai-key\",\"openAiModelId\":\"gpt-4\",\"id\":\"openai-standard-config\"}}},\"globalSettings\":{\"mode\":\"code\"}}'

test_provider "OpenAI (Standard)" "$OPENAI_STANDARD_CONFIG" "openai" "gpt-4"

echo ""
echo "📊 测试总结"
echo "============"
echo "总测试数: $TOTAL_TESTS"
echo "通过: $PASSED_TESTS"
echo "失败: $FAILED_TESTS"
echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 所有Provider配置传递测试通过！"
else
    echo "⚠️  有 $FAILED_TESTS 个Provider测试失败，请检查配置适配逻辑"
fi

echo ""
echo "🔍 查看详细 Worker 日志："
echo "docker compose logs worker --tail=200"
echo ""
echo "🌐 查看监控面板："
echo "http://localhost:3002/admin/queues"
