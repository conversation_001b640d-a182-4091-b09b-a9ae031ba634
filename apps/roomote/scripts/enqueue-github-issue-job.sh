#!/bin/bash

REPO="village-way/Roo-Code"
ISSUE_NUMBER="$1"
REPO_ARG="$2"
API_PROVIDER="${3:-zhanlu}"  # 默认使用zhanlu
MODEL_NAME="${4:-zhanluA<PERSON>}"  # 默认使用zhanluAI模型
BASE_URL="http://localhost:3001"
JOBS_ENDPOINT="$BASE_URL/api/jobs"

if [ -z "$ISSUE_NUMBER" ]; then
    echo "Usage: $0 <issue_number> [repo] [api_provider] [model_name]"
    echo ""
    echo "Examples:"
    echo "  $0 4567                              # Fetch issue #4567 from village-way/Roo-Code using zhanlu/zhanluAI"
    echo "  $0 123 owner/repo                    # Fetch issue #123 from owner/repo using zhanlu/zhanluAI"
    echo "  $0 123 owner/repo openrouter         # Fetch issue #123 using openrouter with default model"
    echo "  $0 123 owner/repo anthropic claude-3-sonnet  # Fetch issue #123 using anthropic/claude-3-sonnet"
    echo "  $0 123 \"\" zhanlu zhanluAI           # Use default repo but specify zhanlu/zhanluAI"
    echo ""
    echo "Parameters:"
    echo "  issue_number : GitHub issue number (required)"
    echo "  repo        : Repository in format 'owner/repo' (optional, default: village-way/Roo-Code)"
    echo "  api_provider: API provider (optional, default: zhanlu)"
    echo "  model_name  : Model name (optional, default: zhanluAI)"
    echo ""
    echo "This script fetches real GitHub issue data and enqueues it as a job."
    exit 1
fi

# 处理repo参数，如果是空字符串则使用默认值
if [ -n "$REPO_ARG" ] && [ "$REPO_ARG" != '""' ] && [ "$REPO_ARG" != "''" ]; then
    REPO="$REPO_ARG"
fi

echo "Configuration:"
echo "  Repository: $REPO"
echo "  Issue: #$ISSUE_NUMBER"
echo "  API Provider: $API_PROVIDER"
echo "  Model: $MODEL_NAME"
echo ""

if ! command -v gh &> /dev/null; then
    echo "Error: GitHub CLI (gh) is not installed. Please install it first."
    echo "Visit: https://cli.github.com/"
    exit 1
fi

if ! gh auth status &> /dev/null; then
    echo "Error: Not authenticated with GitHub CLI. Please run 'gh auth login' first."
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo "Error: jq is not installed. Please install it first."
    echo "Visit: https://jqlang.github.io/jq/download/"
    exit 1
fi

echo "Fetching issue #${ISSUE_NUMBER} from ${REPO}..."

ISSUE_DATA=$(gh issue view ${ISSUE_NUMBER} --repo ${REPO} --json title,body,labels 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "Error: Failed to fetch issue #${ISSUE_NUMBER} from ${REPO}"
    echo "Please check that the issue exists and you have access to the repository."
    exit 1
fi

TITLE=$(echo "$ISSUE_DATA" | jq -r '.title')
BODY=$(echo "$ISSUE_DATA" | jq -r '.body // ""')
LABELS=$(echo "$ISSUE_DATA" | jq -r '[.labels[].name] | @json')

JSON_PAYLOAD=$(jq -n \
  --arg type "github.issue.fix" \
  --arg repo "$REPO" \
  --argjson issue "$ISSUE_NUMBER" \
  --arg title "$TITLE" \
  --arg body "$BODY" \
  --argjson labels "$LABELS" \
  --arg apiProvider "$API_PROVIDER" \
  --arg modelName "$MODEL_NAME" \
  '{
    type: $type,
    payload: {
      repo: $repo,
      issue: $issue,
      title: $title,
      body: $body,
      labels: $labels,
      apiProvider: $apiProvider,
      modelName: $modelName
    }
  }')

echo "Sending request with payload:"
echo "$JSON_PAYLOAD" | jq '.'
echo ""

echo "curl -X POST \"$JOBS_ENDPOINT\" -H \"Content-Type: application/json\" -d \"$JSON_PAYLOAD\" -w \"\nStatus: %{http_code}\n\n\""

curl -X POST "$JOBS_ENDPOINT" -H "Content-Type: application/json" -d "$JSON_PAYLOAD" -w "\nStatus: %{http_code}\n\n"
