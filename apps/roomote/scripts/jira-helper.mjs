#!/usr/bin/env node

/**
 * JIRA Helper Script - 使用 jira.js 库获取JIRA工单信息
 * 
 * 此脚本通过 JIRA REST API v2 获取工单详细信息
 * 并以JSON格式输出结果供bash脚本使用
 */

import { Version2Client } from 'jira.js';
import process from 'process';

// 命令行参数解析
const args = process.argv.slice(2);

if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.error(`
用法: node jira-helper.mjs <command> [options]

命令:
  get-issue <ticket>     获取JIRA工单信息

环境变量:
  JIRA_URL              JIRA系统地址 (必需)
  JIRA_EMAIL            JIRA邮箱地址 (必需)
  JIRA_API_TOKEN        JIRA API令牌 (必需)

示例:
  node jira-helper.mjs get-issue YDYCMKK-850
    `);
    process.exit(args.includes('--help') || args.includes('-h') ? 0 : 1);
}

// 验证环境变量
function validateEnvironment() {
    const required = ['JIRA_URL', 'JIRA_EMAIL', 'JIRA_API_TOKEN'];
    const missing = required.filter(env => !process.env[env]);
    
    if (missing.length > 0) {
        console.error('❌ 缺少必需的环境变量:');
        missing.forEach(env => console.error(`  - ${env}`));
        process.exit(1);
    }
    
    return {
        url: process.env.JIRA_URL,
        email: process.env.JIRA_EMAIL,
        apiToken: process.env.JIRA_API_TOKEN
    };
}

// 创建JIRA客户端
function createJiraClient(config) {
    return new Version2Client({
        host: config.url,
        authentication: {
            basic: {
                email: config.email,
                apiToken: config.apiToken
            }
        }
    });
}

// 获取JIRA工单信息
async function getIssue(client, issueKey) {
    try {
        const issue = await client.issues.getIssue({
            issueIdOrKey: issueKey,
            expand: ['fields']
        });
        
        // 提取关键信息
        const result = {
            success: true,
            data: {
                key: issue.key,
                id: issue.id,
                summary: issue.fields.summary || '无标题',
                description: issue.fields.description || '无描述',
                status: {
                    name: issue.fields.status?.name || '未知状态',
                    id: issue.fields.status?.id || '',
                    category: issue.fields.status?.statusCategory?.name || ''
                },
                priority: {
                    name: issue.fields.priority?.name || '未设置',
                    id: issue.fields.priority?.id || ''
                },
                assignee: {
                    displayName: issue.fields.assignee?.displayName || '未分配',
                    emailAddress: issue.fields.assignee?.emailAddress || '',
                    accountId: issue.fields.assignee?.accountId || ''
                },
                reporter: {
                    displayName: issue.fields.reporter?.displayName || '未知',
                    emailAddress: issue.fields.reporter?.emailAddress || '',
                    accountId: issue.fields.reporter?.accountId || ''
                },
                project: {
                    key: issue.fields.project?.key || '',
                    name: issue.fields.project?.name || ''
                },
                issueType: {
                    name: issue.fields.issuetype?.name || '',
                    id: issue.fields.issuetype?.id || ''
                },
                created: issue.fields.created,
                updated: issue.fields.updated,
                dueDate: issue.fields.duedate,
                labels: issue.fields.labels || [],
                components: issue.fields.components?.map(c => ({
                    name: c.name,
                    id: c.id
                })) || [],
                fixVersions: issue.fields.fixVersions?.map(v => ({
                    name: v.name,
                    id: v.id
                })) || []
            }
        };
        
        return result;
    } catch (error) {
        return {
            success: false,
            error: {
                message: error.message || '获取JIRA工单失败',
                code: error.response?.status || 'UNKNOWN',
                details: error.response?.data || {}
            }
        };
    }
}

// 主函数
async function main() {
    const command = args[0];
    
    try {
        const config = validateEnvironment();
        const client = createJiraClient(config);
        
        switch (command) {
            case 'get-issue': {
                if (args.length < 2) {
                    console.error('❌ 缺少工单编号');
                    console.error('用法: node jira-helper.mjs get-issue <ticket>');
                    process.exit(1);
                }
                
                const ticket = args[1];
                const result = await getIssue(client, ticket);
                
                // 输出JSON结果
                console.log(JSON.stringify(result, null, 2));
                
                // 根据结果设置退出码
                process.exit(result.success ? 0 : 1);
                break;
            }
            
            default:
                console.error(`❌ 未知命令: ${command}`);
                console.error('支持的命令: get-issue');
                process.exit(1);
        }
    } catch (error) {
        console.error('❌ 脚本执行失败:', error.message);
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error.message);
    process.exit(1);
});

// 运行主函数
main().catch(error => {
    console.error('❌ 主函数执行失败:', error.message);
    process.exit(1);
});