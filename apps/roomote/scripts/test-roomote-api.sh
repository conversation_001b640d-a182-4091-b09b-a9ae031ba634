#!/bin/bash

# 测试新的 Roomote API 功能
# 验证 roomote.custom 和 roomote.jira.gitlab 任务类型

set -euo pipefail

BASE_URL="http://localhost:3001"
JOBS_ENDPOINT="$BASE_URL/api/jobs"

echo "=========================================="
echo "🧪 Roomote API 功能测试"
echo "=========================================="
echo ""

# 检查 API 服务状态
echo "🔍 检查 API 服务状态..."
if ! curl -s "$BASE_URL/api/health" > /dev/null 2>&1; then
    echo "❌ 错误: Roomote API 服务未运行"
    echo "   请先启动服务: docker compose up api"
    exit 1
fi
echo "✅ API 服务运行正常"
echo ""

# 测试用例数组
declare -a test_cases=(
    "roomote.custom:自定义任务测试"
    "gitlab.jira.fix:增强版JIRA+GitLab任务测试"
)

echo "🚀 开始 API 测试..."
echo ""

# 测试 roomote.custom 任务
echo "──────────────────────────────────────────"
echo "测试 1: roomote.custom 任务类型"
echo "──────────────────────────────────────────"

CUSTOM_PAYLOAD=$(cat <<'EOF'
{
  "type": "roomote.custom",
  "payload": {
    "customInstructions": "请在项目中添加一个新的工具函数，用于处理字符串格式化，包含基本的大小写转换和空格处理功能",
    "gitlab": {
      "repo": "test-user/demo-project",
      "repoName": "demo-project", 
      "defaultBranch": "main",
      "webUrl": "https://gitlab.example.com/test-user/demo-project",
      "branch": "feature/string-utils"
    },
    "mode": "code",
    "apiProvider": "zhanlu",
    "modelName": "zhanluAI"
  }
}
EOF
)

echo "📤 发送 roomote.custom 任务请求..."
CUSTOM_RESPONSE=$(curl -s -X POST "$JOBS_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$CUSTOM_PAYLOAD")

if echo "$CUSTOM_RESPONSE" | jq -e '.jobId' > /dev/null 2>&1; then
    JOB_ID=$(echo "$CUSTOM_RESPONSE" | jq -r '.jobId')
    echo "✅ roomote.custom 任务创建成功"
    echo "   Job ID: $JOB_ID"
    echo "   响应: $CUSTOM_RESPONSE"
else
    echo "❌ roomote.custom 任务创建失败"
    echo "   响应: $CUSTOM_RESPONSE"
fi

echo ""

# 测试 gitlab.jira.fix 任务（包含自定义指令支持）
echo "──────────────────────────────────────────"
echo "测试 2: gitlab.jira.fix 任务类型（增强版）"
echo "──────────────────────────────────────────"

JIRA_GITLAB_PAYLOAD=$(cat <<'EOF'
{
  "type": "gitlab.jira.fix",
  "payload": {
    "jira": {
      "ticket": "DEMO-123",
      "summary": "修复登录页面响应式布局问题",
      "description": "在移动设备上，登录表单显示不正确，需要优化CSS以确保在不同屏幕尺寸下的正确显示",
      "priority": "High",
      "assignee": "<EMAIL>",
      "status": "In Progress"
    },
    "gitlab": {
      "repo": "company/web-app",
      "repoName": "web-app",
      "defaultBranch": "develop",
      "webUrl": "https://gitlab.company.com/company/web-app",
      "branch": "fix/DEMO-123-responsive-login"
    },
    "customInstructions": "请使用CSS Grid布局系统，确保兼容IE11以上浏览器，并添加适当的媒体查询断点",
    "mode": "code",
    "apiProvider": "zhanlu",
    "modelName": "zhanluAI"
  }
}
EOF
)

echo "📤 发送 gitlab.jira.fix 任务请求..."
JIRA_GITLAB_RESPONSE=$(curl -s -X POST "$JOBS_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$JIRA_GITLAB_PAYLOAD")

if echo "$JIRA_GITLAB_RESPONSE" | jq -e '.jobId' > /dev/null 2>&1; then
    JOB_ID=$(echo "$JIRA_GITLAB_RESPONSE" | jq -r '.jobId')
    echo "✅ gitlab.jira.fix 任务创建成功"
    echo "   Job ID: $JOB_ID"
    echo "   响应: $JIRA_GITLAB_RESPONSE"
else
    echo "❌ gitlab.jira.fix 任务创建失败"
    echo "   响应: $JIRA_GITLAB_RESPONSE"
fi

echo ""

# 测试向后兼容性（使用旧的 modelId 字段）
echo "──────────────────────────────────────────"
echo "测试 3: API 字段简化验证"
echo "──────────────────────────────────────────"

SIMPLIFIED_PAYLOAD=$(cat <<'EOF'
{
  "type": "gitlab.jira.fix",
  "payload": {
    "jira": {
      "ticket": "SIMPLE-789",
      "summary": "测试简化后的字段结构",
      "description": "验证移除重复字段后的API功能",
      "priority": "Medium",
      "assignee": "<EMAIL>",
      "status": "Open"
    },
    "gitlab": {
      "repo": "test/simplified-project",
      "repoName": "simplified-project",
      "defaultBranch": "main",
      "webUrl": "https://gitlab.test.com/test/simplified-project"
    },
    "apiProvider": "zhanlu",
    "modelName": "zhanluAI"
  }
}
EOF
)

echo "📤 发送简化字段测试请求..."
SIMPLIFIED_RESPONSE=$(curl -s -X POST "$JOBS_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$SIMPLIFIED_PAYLOAD")

if echo "$SIMPLIFIED_RESPONSE" | jq -e '.jobId' > /dev/null 2>&1; then
    JOB_ID=$(echo "$SIMPLIFIED_RESPONSE" | jq -r '.jobId')
    echo "✅ 字段简化测试成功"
    echo "   Job ID: $JOB_ID"
    echo "   响应: $SIMPLIFIED_RESPONSE"
else
    echo "❌ 字段简化测试失败"
    echo "   响应: $SIMPLIFIED_RESPONSE"
fi

echo ""
echo "=========================================="
echo "🎉 API 测试完成！"
echo "=========================================="
echo ""
echo "💡 测试总结:"
echo "   • roomote.custom: 支持纯自定义指令任务"
echo "   • gitlab.jira.fix: 统一的JIRA+GitLab集成（支持自定义指令）"
echo "   • 字段简化: 统一使用 modelName，移除重复的 modelId"
echo ""
echo "📚 新功能说明:"
echo "   • 自定义指令: customInstructions 字段"
echo "   • 执行模式: mode 字段（如 'code', 'architect'）"
echo "   • 分支指定: gitlab.branch 字段"
echo "   • 模型统一: 使用 modelName 字段"
echo ""
echo "📊 监控任务执行："
echo "   🌐 队列监控: http://localhost:3002/admin/queues"
echo "   📝 实时日志: docker compose logs -f worker"
echo ""
echo "💡 注意事项："
echo "   • 这些是API层面的测试，实际任务执行需要worker服务"
echo "   • 确保GitLab PAT和JIRA认证信息已正确配置"
echo "   • 测试任务会进入队列等待worker处理"
echo "" 