#!/bin/bash

# 分支支持功能测试脚本
# 用于验证动态代码库的分支指定功能是否正常工作

echo "=========================================="
echo "🧪 分支支持功能测试"
echo "=========================================="
echo ""

BASE_URL="http://localhost:3001"
TEST_REPO="microsoft/vscode"  # 使用Microsoft VSCode作为测试仓库
TEST_ISSUE="1"  # 使用一个通用的issue编号

echo "📋 测试配置："
echo "  仓库: $TEST_REPO"
echo "  Issue: #$TEST_ISSUE"
echo "  服务器: $BASE_URL"
echo ""

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s "$BASE_URL/health" > /dev/null 2>&1; then
    echo "❌ 错误: Roomote服务未运行"
    echo "   请先启动服务: docker compose up"
    exit 1
fi
echo "✅ 服务运行正常"
echo ""

# 测试用例
declare -a test_cases=(
    "main:默认主分支"
    "insiders:VS Code内测分支"
    "release/1.85:发布分支"
    "nonexistent:不存在的分支"
)

echo "🚀 开始分支测试..."
echo ""

for test_case in "${test_cases[@]}"; do
    IFS=':' read -r branch description <<< "$test_case"
    
    echo "──────────────────────────────────────────"
    echo "测试: $description"
    echo "分支: $branch"
    echo "──────────────────────────────────────────"
    
    # 构建测试payload
    JSON_PAYLOAD=$(jq -n \
      --arg type "github.issue.fix" \
      --arg repo "$TEST_REPO" \
      --arg branch "$branch" \
      --argjson issue "$TEST_ISSUE" \
      --arg title "Test branch support for $branch" \
      --arg body "This is a test for branch support functionality." \
      --argjson labels '["test", "branch-support"]' \
      --arg apiProvider "zhanlu" \
      --arg modelName "zhanluAI" \
      '{
        type: $type,
        payload: {
          repo: $repo,
          branch: $branch,
          issue: $issue,
          title: $title,
          body: $body,
          labels: $labels,
          apiProvider: $apiProvider,
          modelName: $modelName
        }
      }')
    
    echo "📦 测试载荷:"
    echo "$JSON_PAYLOAD" | jq '.'
    echo ""
    
    echo "🚀 发送请求..."
    RESPONSE=$(curl -s -X POST "$BASE_URL/api/jobs" \
      -H "Content-Type: application/json" \
      -d "$JSON_PAYLOAD" \
      -w "HTTP_STATUS:%{http_code}")
    
    HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
    
    if [ "$HTTP_STATUS" -eq 200 ]; then
        echo "✅ 任务创建成功"
        
        JOB_ID=$(echo "$RESPONSE_BODY" | jq -r '.jobId // "unknown"')
        echo "   Job ID: $JOB_ID"
        
        # 检查payload是否正确包含分支信息
        if echo "$RESPONSE_BODY" | jq -e '.payload.branch' > /dev/null 2>&1; then
            ACTUAL_BRANCH=$(echo "$RESPONSE_BODY" | jq -r '.payload.branch')
            echo "   ✅ 分支字段正确: $ACTUAL_BRANCH"
        else
            echo "   ⚠️  分支字段缺失"
        fi
    else
        echo "❌ 任务创建失败 (HTTP $HTTP_STATUS)"
        echo "   错误信息: $RESPONSE_BODY"
    fi
    
    echo ""
    sleep 1  # 避免请求过于频繁
done

echo "=========================================="
echo "🎯 测试总结"
echo "=========================================="
echo ""
echo "✅ 分支支持功能测试完成"
echo ""
echo "📊 如需查看详细执行日志："
echo "   docker compose logs -f worker"
echo ""
echo "📈 如需监控任务队列："
echo "   浏览器访问: http://localhost:3002/admin/queues"
echo ""
echo "💡 注意事项："
echo "   • 不存在的分支会自动回退到默认分支"
echo "   • 所有测试任务都会进入队列等待执行"
echo "   • 实际执行会检查分支是否存在于远程仓库"
echo "" 