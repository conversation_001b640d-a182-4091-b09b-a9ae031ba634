#!/bin/bash

# GitLab集成测试脚本
# 验证GitLab CLI、认证和API连接是否正常工作

set -euo pipefail

echo "🧪 GitLab集成测试"
echo "==========================================="

# 检查环境变量
if [ -z "${GITLAB_PAT:-}" ]; then
    echo "❌ GITLAB_PAT环境变量未设置"
    echo "请设置GITLAB_PAT环境变量后重试"
    exit 1
fi

if [ -z "${GITLAB_URL:-}" ]; then
    echo "⚠️  GITLAB_URL环境变量未设置，使用默认值"
    GITLAB_URL="http://gitlab.cmss.com"
fi

echo "📍 GitLab URL: $GITLAB_URL"
echo ""

# 测试1：容器内GitLab CLI可用性
echo "📋 测试1：容器内GitLab CLI可用性"
if docker run --rm roomote-worker which glab >/dev/null 2>&1; then
    echo "✅ glab CLI已安装"
else
    echo "❌ glab CLI未安装"
    exit 1
fi

if docker run --rm roomote-worker which git >/dev/null 2>&1; then
    echo "✅ git已安装"
else
    echo "❌ git未安装"
    exit 1
fi
echo ""

# 测试2：容器内GitLab认证配置
echo "📋 测试2：容器内GitLab认证配置"
if docker run --rm -e GITLAB_PAT="$GITLAB_PAT" -e GITLAB_URL="$GITLAB_URL" roomote-worker \
    /bin/bash -c "/usr/local/bin/configure-gitlab-auth.sh && test -f /root/.config/glab-cli/config.yml"; then
    echo "✅ 认证配置脚本执行成功"
else
    echo "❌ 认证配置脚本执行失败"
    exit 1
fi
echo ""

# 测试3：GitLab API连接测试
echo "📋 测试3：GitLab API连接测试"
GITLAB_HOST=$(echo "$GITLAB_URL" | sed -e 's|^[^/]*//||' -e 's|/.*$||')
if curl -s -f -H "Authorization: Bearer $GITLAB_PAT" "$GITLAB_URL/api/v4/user" >/dev/null 2>&1; then
    echo "✅ GitLab API连接成功"
else
    echo "❌ GitLab API连接失败"
    echo "请检查GITLAB_URL和GITLAB_PAT是否正确"
    exit 1
fi
echo ""

# 测试4：容器内glab认证测试
echo "📋 测试4：容器内glab认证测试"
if docker run --rm -e GITLAB_PAT="$GITLAB_PAT" -e GITLAB_URL="$GITLAB_URL" roomote-worker \
    /bin/bash -c "/usr/local/bin/configure-gitlab-auth.sh && glab auth status" >/dev/null 2>&1; then
    echo "✅ glab认证测试通过"
else
    echo "⚠️  glab认证测试失败（可能是正常的，取决于GitLab版本）"
fi
echo ""

# 测试5：Worker容器功能测试
echo "📋 测试5：Worker容器功能测试"
if docker run --rm -e GITLAB_PAT="$GITLAB_PAT" -e GITLAB_URL="$GITLAB_URL" roomote-worker \
    /bin/bash -c "node --version && npm --version" >/dev/null 2>&1; then
    echo "✅ Worker容器基础功能正常"
else
    echo "❌ Worker容器基础功能异常"
    exit 1
fi
echo ""

# 可选测试6：测试仓库访问（如果设置了TEST_GITLAB_REPO）
if [ -n "${TEST_GITLAB_REPO:-}" ]; then
    echo "📋 测试6：测试仓库访问"
    if docker run --rm -e GITLAB_PAT="$GITLAB_PAT" -e GITLAB_URL="$GITLAB_URL" roomote-worker \
        /bin/bash -c "/usr/local/bin/configure-gitlab-auth.sh && glab repo view $TEST_GITLAB_REPO" >/dev/null 2>&1; then
        echo "✅ 测试仓库 $TEST_GITLAB_REPO 访问成功"
    else
        echo "❌ 测试仓库 $TEST_GITLAB_REPO 访问失败"
        echo "请检查仓库名称和权限"
    fi
    echo ""
fi

echo "🎉 GitLab集成测试完成！"
echo ""
echo "所有核心功能测试通过，你的GitLab集成已准备就绪。"
echo ""
echo "接下来可以："
echo "1. 启动服务：docker compose up -d"
echo "2. 测试GitLab工作流：./scripts/enqueue-gitlab-repo-job.sh --help"
echo "3. 访问管理界面：http://localhost:3002/admin/queues" 