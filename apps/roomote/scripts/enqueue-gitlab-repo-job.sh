#!/bin/bash

# GitLab + JIRA 工作流自动化脚本
# 将JIRA工单信息与GitLab仓库集成，创建自动化修复任务

set -euo pipefail

# 脚本配置
BASE_URL="http://localhost:3001"
JOBS_ENDPOINT="$BASE_URL/api/jobs"

# 默认值
DEFAULT_API_PROVIDER="zhanlu"
DEFAULT_MODEL_ID="zhanluAI"
DEFAULT_TYPE="gitlab.jira.fix"

# 参数解析
JIRA_TICKET=""
GITLAB_REPO=""
API_PROVIDER="$DEFAULT_API_PROVIDER"
MODEL_ID="$DEFAULT_MODEL_ID"
TYPE="$DEFAULT_TYPE"
DEBUG=false

# 显示帮助信息
show_help() {
    echo "=========================================="
    echo "🚀 GitLab + JIRA 工作流自动化脚本"
    echo "=========================================="
    echo ""
    echo "用法:"
    echo "  $0 --jira-ticket <JIRA编号> --gitlab-repo <仓库路径> [选项]"
    echo ""
    echo "必需参数:"
    echo "  --jira-ticket    JIRA工单编号 (如: YDYCMKK-850)"
    echo "  --gitlab-repo    GitLab仓库路径 (如: paas-aipt/ai/zhanlu-vs)"
    echo ""
    echo "可选参数:"
    echo "  --api-provider   API服务提供商 (默认: $DEFAULT_API_PROVIDER)"
    echo "  --model-id       AI模型标识符 (默认: $DEFAULT_MODEL_ID)"
    echo "  --type           任务类型 (默认: $DEFAULT_TYPE)"
    echo "  --debug          启用调试模式"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "环境变量配置:"
    echo "  JIRA_URL         JIRA系统地址 (默认: http://jira.cmss.com)"
    echo "  JIRA_USERNAME    JIRA用户名"
    echo "  JIRA_PASSWORD    JIRA密码"
    echo "  GITLAB_URL       GitLab系统地址 (默认: http://gitlab.cmss.com)"
    echo "  GITLAB_PAT       GitLab Personal Access Token"
    echo ""
    echo "使用示例:"
    echo "  # 基础用法"
    echo "  $0 --jira-ticket YDYCMKK-850 --gitlab-repo paas-aipt/ai/zhanlu-vs"
    echo ""
    echo "  # 指定AI模型"
    echo "  $0 --jira-ticket PROJ-123 --gitlab-repo group/project \\"
    echo "       --api-provider openrouter --model-id gpt-4-turbo"
    echo ""
    echo "  # 自定义任务类型"
    echo "  $0 --jira-ticket BUG-456 --gitlab-repo team/webapp \\"
    echo "       --type gitlab.jira.enhancement"
    echo ""
    echo "功能特性:"
    echo "  ✅ JIRA REST API v2 集成"
    echo "  ✅ GitLab API 完整支持"
    echo "  ✅ 多种AI模型支持"
    echo "  ✅ 自动权限验证"
    echo "  ✅ 详细日志输出"
    echo "  ✅ 错误处理机制"
    echo ""
}

# 日志函数
log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "✅ $1"
}

log_warning() {
    echo "⚠️  $1"
}

log_error() {
    echo "❌ $1" >&2
}

log_debug() {
    if [ "$DEBUG" = true ]; then
        echo "[DEBUG] $1"
    fi
}

# 参数解析
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --jira-ticket)
                JIRA_TICKET="$2"
                shift 2
                ;;
            --gitlab-repo)
                GITLAB_REPO="$2"
                shift 2
                ;;
            --api-provider)
                API_PROVIDER="$2"
                shift 2
                ;;
            --model-id)
                MODEL_ID="$2"
                shift 2
                ;;
            --type)
                TYPE="$2"
                shift 2
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                echo ""
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证必需参数
validate_required_params() {
    if [ -z "$JIRA_TICKET" ]; then
        log_error "缺少必需参数: --jira-ticket"
        echo ""
        show_help
        exit 1
    fi

    if [ -z "$GITLAB_REPO" ]; then
        log_error "缺少必需参数: --gitlab-repo"
        echo ""
        show_help
        exit 1
    fi
}

# 验证环境变量
validate_environment() {
    local missing_vars=()

    # JIRA配置检查
    if [ -z "${JIRA_USERNAME:-}" ]; then
        missing_vars+=("JIRA_USERNAME")
    fi
    if [ -z "${JIRA_PASSWORD:-}" ]; then
        missing_vars+=("JIRA_PASSWORD")
    fi

    # GitLab配置检查
    if [ -z "${GITLAB_PAT:-}" ]; then
        missing_vars+=("GITLAB_PAT")
    fi

    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "缺少必需的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "请配置所有必需的环境变量后重新运行脚本。"
        exit 1
    fi
}

# 验证依赖工具
check_dependencies() {
    local missing_tools=()

    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi

    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi

    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi

    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必需工具:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "请安装所有必需工具后重新运行脚本。"
        echo ""
        echo "安装指南:"
        echo "  curl: https://curl.se/download.html"
        echo "  jq: https://jqlang.github.io/jq/download/"
        echo "  node: https://nodejs.org/"
        exit 1
    fi
}

# JIRA API集成
fetch_jira_issue() {
    local ticket="$1"
    local jira_url="${JIRA_URL:-http://jira.cmss.com}"
    
    log_info "正在获取JIRA工单信息: $ticket"
    log_debug "JIRA URL: $jira_url"
    
    # 构建JIRA API请求
    local jira_api_url="$jira_url/rest/api/2/issue/$ticket"
    
    log_debug "API请求URL: $jira_api_url"
    
    # 发送API请求
    local response
    response=$(curl -s -w "HTTP_STATUS:%{http_code}" \
        -u "$JIRA_USERNAME:$JIRA_PASSWORD" \
        -H "Accept: application/json" \
        "$jira_api_url")
    
    local http_status
    http_status=$(echo "$response" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    local response_body
    response_body=$(echo "$response" | sed 's/HTTP_STATUS:[0-9]*$//')
    
    log_debug "HTTP状态码: $http_status"
    
    if [ "$http_status" -ne 200 ]; then
        log_error "JIRA API请求失败 (HTTP $http_status)"
        if [ "$DEBUG" = true ]; then
            echo "响应内容: $response_body"
        fi
        case $http_status in
            401)
                echo "  可能原因: 认证失败，请检查JIRA_USERNAME和JIRA_PASSWORD"
                ;;
            404)
                echo "  可能原因: 工单 $ticket 不存在或无访问权限"
                ;;
            *)
                echo "  响应内容: $response_body"
                ;;
        esac
        exit 1
    fi
    
    # 解析JIRA响应
    local jira_data
    if ! jira_data=$(echo "$response_body" | jq -c .); then
        log_error "无法解析JIRA响应数据"
        exit 1
    fi
    
    # 提取关键信息
    local summary description priority assignee status
    summary=$(echo "$jira_data" | jq -r '.fields.summary // "无标题"')
    description=$(echo "$jira_data" | jq -r '.fields.description // "无描述"')
    priority=$(echo "$jira_data" | jq -r '.fields.priority.name // "未设置"')
    assignee=$(echo "$jira_data" | jq -r '.fields.assignee.displayName // "未分配"')
    status=$(echo "$jira_data" | jq -r '.fields.status.name // "未知状态"')
    
    log_success "JIRA工单信息获取成功"
    echo "  标题: $summary"
    echo "  状态: $status"
    echo "  优先级: $priority"
    echo "  指派人: $assignee"
    
    # 将信息存储为全局变量供后续使用
    JIRA_SUMMARY="$summary"
    JIRA_DESCRIPTION="$description"
    JIRA_PRIORITY="$priority"
    JIRA_ASSIGNEE="$assignee"
    JIRA_STATUS="$status"
    JIRA_DATA="$jira_data"
}

# GitLab API集成
validate_gitlab_repo() {
    local repo="$1"
    local gitlab_url="${GITLAB_URL:-http://gitlab.cmss.com}"
    
    log_info "验证GitLab仓库访问权限: $repo"
    log_debug "GitLab URL: $gitlab_url"
    
    # 构建GitLab API请求
    local encoded_repo
    encoded_repo=$(echo "$repo" | sed 's/\//%2F/g')
    local gitlab_api_url="$gitlab_url/api/v4/projects/$encoded_repo"
    
    log_debug "API请求URL: $gitlab_api_url"
    
    # 发送API请求
    local response
    response=$(curl -s -w "HTTP_STATUS:%{http_code}" \
        -H "Authorization: Bearer $GITLAB_PAT" \
        -H "Content-Type: application/json" \
        "$gitlab_api_url")
    
    local http_status
    http_status=$(echo "$response" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    local response_body
    response_body=$(echo "$response" | sed 's/HTTP_STATUS:[0-9]*$//')
    
    log_debug "HTTP状态码: $http_status"
    
    if [ "$http_status" -ne 200 ]; then
        log_error "GitLab API请求失败 (HTTP $http_status)"
        if [ "$DEBUG" = true ]; then
            echo "响应内容: $response_body"
        fi
        case $http_status in
            401)
                echo "  可能原因: 认证失败，请检查GITLAB_PAT"
                ;;
            404)
                echo "  可能原因: 仓库 $repo 不存在或无访问权限"
                ;;
            *)
                echo "  响应内容: $response_body"
                ;;
        esac
        exit 1
    fi
    
    # 解析GitLab响应
    local gitlab_data
    if ! gitlab_data=$(echo "$response_body" | jq -c .); then
        log_error "无法解析GitLab响应数据"
        exit 1
    fi
    
    # 提取仓库信息
    local repo_name default_branch web_url
    repo_name=$(echo "$gitlab_data" | jq -r '.name // "未知"')
    default_branch=$(echo "$gitlab_data" | jq -r '.default_branch // "main"')
    web_url=$(echo "$gitlab_data" | jq -r '.web_url // ""')
    
    log_success "GitLab仓库验证成功"
    echo "  仓库名称: $repo_name"
    echo "  默认分支: $default_branch"
    echo "  访问地址: $web_url"
    
    # 将信息存储为全局变量
    GITLAB_REPO_NAME="$repo_name"
    GITLAB_DEFAULT_BRANCH="$default_branch"
    GITLAB_WEB_URL="$web_url"
    GITLAB_DATA="$gitlab_data"
}

# 构建任务载荷
build_task_payload() {
    log_info "构建任务载荷..."
    
    local payload
    payload=$(jq -n \
        --arg type "$TYPE" \
        --arg jiraTicket "$JIRA_TICKET" \
        --arg jiraSummary "$JIRA_SUMMARY" \
        --arg jiraDescription "$JIRA_DESCRIPTION" \
        --arg jiraPriority "$JIRA_PRIORITY" \
        --arg jiraAssignee "$JIRA_ASSIGNEE" \
        --arg jiraStatus "$JIRA_STATUS" \
        --arg gitlabRepo "$GITLAB_REPO" \
        --arg gitlabRepoName "$GITLAB_REPO_NAME" \
        --arg gitlabDefaultBranch "$GITLAB_DEFAULT_BRANCH" \
        --arg gitlabWebUrl "$GITLAB_WEB_URL" \
        --arg apiProvider "$API_PROVIDER" \
        --arg modelId "$MODEL_ID" \
        '{
            type: $type,
            payload: {
                jira: {
                    ticket: $jiraTicket,
                    summary: $jiraSummary,
                    description: $jiraDescription,
                    priority: $jiraPriority,
                    assignee: $jiraAssignee,
                    status: $jiraStatus
                },
                gitlab: {
                    repo: $gitlabRepo,
                    repoName: $gitlabRepoName,
                    defaultBranch: $gitlabDefaultBranch,
                    webUrl: $gitlabWebUrl
                },
                apiProvider: $apiProvider,
                modelId: $modelId
            }
        }')
    
    TASK_PAYLOAD="$payload"
    log_success "任务载荷构建完成"
}

# 提交任务到队列
enqueue_task() {
    log_info "提交任务到队列..."
    
    log_debug "API端点: $JOBS_ENDPOINT"
    if [ "$DEBUG" = true ]; then
        echo "任务载荷:"
        echo "$TASK_PAYLOAD" | jq '.'
        echo ""
    fi
    
    # 发送任务请求
    local response
    response=$(curl -s -w "HTTP_STATUS:%{http_code}" \
        -X POST "$JOBS_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d "$TASK_PAYLOAD")
    
    local http_status
    http_status=$(echo "$response" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    local response_body
    response_body=$(echo "$response" | sed 's/HTTP_STATUS:[0-9]*$//')
    
    log_debug "HTTP状态码: $http_status"
    
    if [ "$http_status" -eq 200 ]; then
        log_success "任务成功提交!"
        echo ""
        echo "📊 响应信息:"
        echo "$response_body" | jq '.'
        
        local job_id enqueued_job_id
        job_id=$(echo "$response_body" | jq -r '.jobId')
        enqueued_job_id=$(echo "$response_body" | jq -r '.enqueuedJobId')
        
        echo ""
        echo "🎯 任务追踪:"
        echo "  📋 Job ID: $job_id"
        echo "  🔗 Queue ID: $enqueued_job_id"
        echo ""
        echo "📈 监控方式:"
        echo "  🌐 监控面板: http://localhost:3002/admin/queues"
        echo "  📋 任务状态: curl $BASE_URL/api/jobs/$job_id"
        echo "  📝 实时日志: docker compose logs -f worker"
        echo ""
        echo "🔮 预期流程:"
        echo "  1. 🤖 AI代理将分析JIRA工单: $JIRA_TICKET"
        echo "  2. 📂 自动克隆GitLab仓库: $GITLAB_REPO"
        echo "  3. 💻 根据工单描述编写解决方案"
        echo "  4. 🧪 运行测试验证修复效果"
        echo "  5. 📝 创建Merge Request"
        echo "  6. 🔗 自动关联JIRA工单"
        echo ""
        echo "🎉 任务已启动，请等待AI代理完成工作！"
    else
        log_error "任务提交失败 (HTTP $http_status)"
        echo ""
        echo "错误响应:"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        exit 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "🚀 GitLab + JIRA 工作流自动化"
    echo "=========================================="
    echo ""
    
    # 解析参数
    parse_args "$@"
    
    # 验证参数和环境
    validate_required_params
    validate_environment
    check_dependencies
    
    echo "配置信息:"
    echo "  🎫 JIRA工单: $JIRA_TICKET"
    echo "  📂 GitLab仓库: $GITLAB_REPO"
    echo "  🤖 API提供商: $API_PROVIDER"
    echo "  🧠 模型ID: $MODEL_ID"
    echo "  📋 任务类型: $TYPE"
    echo "  🔍 调试模式: $DEBUG"
    echo ""
    
    # 执行主要流程
    fetch_jira_issue "$JIRA_TICKET"
    echo ""
    
    validate_gitlab_repo "$GITLAB_REPO"
    echo ""
    
    build_task_payload
    echo ""
    
    enqueue_task
}

# 错误处理
trap 'log_error "脚本执行失败，位置：第$LINENO行"' ERR

# 执行主函数
main "$@" 