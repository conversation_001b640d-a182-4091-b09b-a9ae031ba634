-- ===================================
-- Roomote 离线数据库迁移脚本
-- 包含所有迁移步骤的完整SQL
-- ===================================

-- 创建数据库（如果需要）
-- CREATE DATABASE cloud_agents;

-- 初始表结构创建 (0000_cuddly_luke_cage.sql)
CREATE TABLE "cloud_jobs" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "cloud_jobs_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"type" text NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"payload" jsonb NOT NULL,
	"result" jsonb,
	"error" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"started_at" timestamp,
	"completed_at" timestamp
);

CREATE TABLE "cloud_tasks" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "cloud_tasks_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"job_id" integer NOT NULL,
	"task_id" integer,
	"container_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);

ALTER TABLE "cloud_tasks" ADD CONSTRAINT "cloud_tasks_job_id_cloud_jobs_id_fk" FOREIGN KEY ("job_id") REFERENCES "public"."cloud_jobs"("id") ON DELETE no action ON UPDATE no action;

-- 添加 Slack 支持字段 (0001_fluffy_sasquatch.sql)
ALTER TABLE "cloud_jobs" ADD COLUMN "slack_thread_ts" text;

-- 删除 cloud_tasks 表 (0002_brief_sentry.sql)
DROP TABLE "cloud_tasks" CASCADE;

-- 添加任务ID字段 (0003_bitter_echo.sql)
ALTER TABLE "cloud_jobs" ADD COLUMN "task_id" text;

-- 插入测试数据（可选）
-- INSERT INTO "cloud_jobs" ("type", "status", "payload") VALUES 
-- ('github.issue.fix', 'pending', '{"repo":"test/repo","issue":1}');

-- 验证表结构
\d cloud_jobs; 