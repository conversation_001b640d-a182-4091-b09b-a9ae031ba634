{"id": "4cae0c18-141d-40a3-acc8-38fcd7f07534", "prevId": "8f65bfed-78de-4e22-a15f-36de8afe5f2e", "version": "7", "dialect": "postgresql", "tables": {"public.cloud_jobs": {"name": "cloud_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "cloud_jobs_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "jsonb", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "slack_thread_ts": {"name": "slack_thread_ts", "type": "text", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cloud_tasks": {"name": "cloud_tasks", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "cloud_tasks_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "job_id": {"name": "job_id", "type": "integer", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "integer", "primaryKey": false, "notNull": false}, "container_id": {"name": "container_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"cloud_tasks_job_id_cloud_jobs_id_fk": {"name": "cloud_tasks_job_id_cloud_jobs_id_fk", "tableFrom": "cloud_tasks", "tableTo": "cloud_jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}