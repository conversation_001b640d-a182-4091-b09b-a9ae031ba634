{"id": "3133cfd6-5929-496b-8012-709fb5fdedf6", "prevId": "16afebd7-b27a-457e-b3d5-4de50a597c2e", "version": "7", "dialect": "postgresql", "tables": {"public.cloud_jobs": {"name": "cloud_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "cloud_jobs_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "jsonb", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false}, "slack_thread_ts": {"name": "slack_thread_ts", "type": "text", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}