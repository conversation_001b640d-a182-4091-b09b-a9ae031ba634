{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Создавайте задачи удаленной разработки с интеграцией JIRA и поддержкой репозитория GitLab.", "jira": {"title": "Соединение JIRA", "description": "Подключитесь к системе JIRA для получения информации о задачах. Это подключение необязательно. Если вы не подключены к JIRA, требуются пользовательские инструкции."}, "gitlab": {"title": "Соединение GitLab", "description": "Подключитесь к системе GitLab для выбора репозитория кода и ветки. Это обязательно, поскольку удаленный агент должен выполнять задачи в репозитории кода."}, "customInstructions": {"title": "Пользовательские инструкции", "description": "Опишите задачу, которую должен выполнить удаленный агент. Это поле обязательно, если вы не подключены к JIRA.", "placeholder": "Введите здесь ваши пользовательские инструкции задачи..."}, "aiSettings": {"title": "Настройка ИИ", "description": "Выберите провайдера ИИ и модель для выполнения задач."}, "createTask": "Создать задачу", "creating": "Создание...", "error": {"gitlabRequired": "Требуется соединение GitLab", "customInstructionsRequired": "Требуются пользовательские инструкции, когда не подключен к JIRA", "createTaskFailed": "Не удалось создать задачу"}, "info": {"taskCreated": "Задача Roomote успешно создана"}}