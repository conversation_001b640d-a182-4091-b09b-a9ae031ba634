{"title": "Remote Agent", "description": "启动一个远程代理并行工作，即使在您关闭笔记本电脑后，它也会在隔离环境中持续运行。", "jira": {"title": "连接到 JIRA", "description": "可选：从 JIRA 工单导入任务详情。", "connect": "连接到 JIRA", "connected": "JIRA 已连接", "disconnect": "断开连接"}, "gitlab": {"title": "连接到 GitLab", "description": "必需：选择代理工作的仓库和分支。", "connect": "连接到 GitLab", "connected": "GitLab 已连接", "disconnect": "断开连接"}, "customInstructions": {"placeholder": "描述您希望代理完成的任务..."}, "aiSettings": {"title": "AI 配置", "description": "选择用于任务执行的 AI 提供商和模型。"}, "createTask": "启动远程代理", "creating": "启动中...", "error": {"gitlabRequired": "需要 GitLab 连接", "customInstructionsRequired": "未连接 JIRA 时需要任务描述", "createTaskFailed": "启动代理失败"}, "info": {"taskCreated": "代理启动成功"}}