{"title": "Agent <PERSON><PERSON>", "description": "Crea tasques de desenvolupament remot amb integració JIRA i suport de repositori GitLab.", "jira": {"title": "Connexió JIRA", "description": "Connecta't al sistema JIRA per obtenir informació de tiquets. Aquesta connexió és opcional. Si no estàs connectat a JIRA, es requereixen instruccions personalitzades."}, "gitlab": {"title": "Connexió GitLab", "description": "Connecta't al sistema GitLab per seleccionar repositori de codi i branca. Això és necessari perquè l'agent remot ha d'executar tasques al repositori de codi."}, "customInstructions": {"title": "Instruccions Personalitzades", "description": "Descriu la tasca que vols que executi l'agent remot. Aquest camp és obligatori si no estàs connectat a JIRA.", "placeholder": "Introdueix les teves instruccions de tasca personalitzades aquí..."}, "aiSettings": {"title": "Configuració AI", "description": "Selecciona el proveïdor d'AI i el model per a l'execució de tasques."}, "createTask": "<PERSON><PERSON><PERSON>", "creating": "Creant...", "error": {"gitlabRequired": "Connexió GitLab requerida", "customInstructionsRequired": "Instruccions personalitzades requerides quan no estàs connectat a JIRA", "createTaskFailed": "Ha fallat la creació de la tasca"}, "info": {"taskCreated": "<PERSON><PERSON> creada amb èxit"}}