{"greeting": "Selamat datang di <PERSON>!", "introduction": "Dengan berbagai Mode bawaan dan dapat diper<PERSON>, <PERSON><PERSON><PERSON> memung<PERSON>kan Anda mere<PERSON>, merancang, coding, debug, dan meningkatkan produktivitas seperti yang belum pernah terjadi sebelumnya.", "notice": "Untuk memulai, ekstensi ini memerlukan provider API.", "start": "Ayo mulai!", "routers": {"requesty": {"description": "Router LLM yang di<PERSON>", "incentive": "Kredit gratis $1"}, "openrouter": {"description": "Interface terpadu untuk LLM"}}, "chooseProvider": "Untuk melakukan k<PERSON>, <PERSON><PERSON><PERSON> membutuhkan API key.", "startRouter": "<PERSON><PERSON> menggunakan Router LLM:", "startCustom": "Atau Anda dapat menggunakan API key Anda sendiri:", "telemetry": {"title": "Bantu Tingkatkan zhanlu", "anonymousTelemetry": "Kirim data error dan penggunaan anonim untuk membantu kami memperbaiki bug dan meningkatkan ekstensi. Tidak ada kode, prompt, atau informasi pribadi yang pernah dikirim.", "changeSettings": "<PERSON>a selalu dapat mengubah ini di bagian bawah <settingsLink>pengaturan</settingsLink>", "settings": "pengat<PERSON><PERSON>", "allow": "Izinkan", "deny": "<PERSON><PERSON>"}, "importSettings": "<PERSON><PERSON><PERSON>"}