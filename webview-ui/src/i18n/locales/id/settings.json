{"common": {"save": "Simpan", "done": "Se<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "reset": "Reset", "select": "<PERSON><PERSON><PERSON>", "add": "Tambah Header", "remove": "Hapus"}, "header": {"title": "<PERSON><PERSON><PERSON><PERSON>", "saveButtonTooltip": "<PERSON><PERSON><PERSON>", "nothingChangedTooltip": "Tidak ada yang berubah", "doneButtonTooltip": "<PERSON><PERSON> per<PERSON>han yang belum disimpan dan tutup panel pengaturan"}, "unsavedChangesDialog": {"title": "Perubahan Belum Disimpan", "description": "A<PERSON><PERSON>h kamu ingin membuang perubahan dan melanjutkan?", "cancelButton": "<PERSON><PERSON>", "discardButton": "<PERSON><PERSON>"}, "sections": {"providers": "Provider", "autoApprove": "Auto-Approve", "browser": "Browser", "checkpoints": "Checkpoint", "notifications": "Notif<PERSON><PERSON>", "contextManagement": "Konteks", "terminal": "Terminal", "prompts": "Prompt", "experimental": "Eksperimental", "language": "Bahasa", "about": "<PERSON><PERSON><PERSON>"}, "developerMode": {"title": "Mode Pengembang", "label": "Aktifkan Mode Pengembang", "description": "Mode pengembang menyediakan fitur lanjutan dan opsi konfigurasi, termasuk fitur eksperimental, pengaturan terminal, manajemen prompt, dan la<PERSON>ya."}, "prompts": {"description": "Konfigurasi support prompt yang digunakan untuk aksi cepat seperti meningkatkan prompt, men<PERSON><PERSON><PERSON> kode, dan memperbaiki masalah. Prompt ini membantu zhanlu memberikan bantuan yang lebih baik untuk tugas pengembangan umum."}, "codeIndex": {"title": "Pengindeksan Codebase", "enableLabel": "Aktifkan Pengindeksan Codebase", "enableDescription": "Aktifkan pengindeksan kode untuk pencarian dan pemahaman konteks yang lebih baik", "providerLabel": "Provider Embeddings", "selectProviderPlaceholder": "Pilih provider", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "API Key:", "geminiApiKeyPlaceholder": "Masukkan kunci API Gemini Anda", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Kunci API:", "mistralApiKeyPlaceholder": "Masukkan kunci API Mistral Anda", "openaiCompatibleProvider": "OpenAI Compatible", "openAiKeyLabel": "OpenAI API Key", "openAiKeyPlaceholder": "Masukkan kunci API OpenAI kamu", "openAiCompatibleBaseUrlLabel": "Base URL", "openAiCompatibleApiKeyLabel": "API Key", "openAiCompatibleApiKeyPlaceholder": "Masukkan kunci API kamu", "openAiCompatibleModelDimensionLabel": "Dimensi Embedding:", "modelDimensionLabel": "Dimensi Model", "openAiCompatibleModelDimensionPlaceholder": "misalnya, 1536", "openAiCompatibleModelDimensionDescription": "Dimensi embedding (ukuran output) untuk model kamu. Periksa dokumentasi provider kamu untuk nilai ini. <PERSON>lai umum: 384, 768, 1536, 3072.", "modelLabel": "Model", "selectModelPlaceholder": "<PERSON><PERSON><PERSON> model", "ollamaUrlLabel": "Ollama URL:", "qdrantUrlLabel": "Qdrant URL", "qdrantKeyLabel": "Qdrant Key:", "startIndexingButton": "<PERSON><PERSON>", "clearIndexDataButton": "<PERSON><PERSON>", "unsavedSettingsMessage": "<PERSON><PERSON>an simpan pengaturan kamu sebelum memulai proses pengindeksan.", "clearDataDialog": {"title": "Apakah kamu yakin?", "description": "Tindakan ini tidak dapat dibatalkan. Ini akan menghapus data indeks codebase kamu secara permanen.", "cancelButton": "<PERSON><PERSON>", "confirmButton": "Hapus Data"}, "description": "Konfigurasi pengaturan pengindeksan codebase untuk mengaktifkan pencarian semantik proyek kamu. <0>Pelajari lebih lanjut</0>", "statusTitle": "Status", "settingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "disabledMessage": "Pengindeksan codebase saat ini dinonaktifkan. Aktifkan di pengaturan global untuk mengkonfigurasi opsi pengindeksan.", "embedderProviderLabel": "Provider <PERSON>", "modelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama model", "selectModel": "<PERSON><PERSON><PERSON> model", "ollamaBaseUrlLabel": "URL Dasar <PERSON>ma", "qdrantApiKeyLabel": "Kunci API Qdrant", "qdrantApiKeyPlaceholder": "Masukkan kunci API Qdrant kamu (opsional)", "setupConfigLabel": "<PERSON><PERSON><PERSON><PERSON>", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "<PERSON><PERSON> <PERSON>", "modelDimensions": "({{dimension}} dimensi)", "saveSuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON><PERSON> disimpan", "saving": "Menyimpan...", "saveSettings": "Simpan", "indexingStatuses": {"standby": "Siaga", "indexing": "<PERSON><PERSON><PERSON><PERSON>", "indexed": "<PERSON><PERSON><PERSON><PERSON>", "error": "Error"}, "close": "<PERSON><PERSON><PERSON>", "validation": {"invalidQdrantUrl": "URL Qdrant tidak valid", "invalidOllamaUrl": "URL Ollama tidak valid", "invalidBaseUrl": "URL dasar tidak valid", "qdrantUrlRequired": "URL Qdrant dip<PERSON><PERSON>an", "openaiApiKeyRequired": "Kunci API OpenAI diperlukan", "modelSelectionRequired": "<PERSON><PERSON><PERSON>han model <PERSON><PERSON><PERSON><PERSON>", "apiKeyRequired": "Kunci API diperlukan", "modelIdRequired": "ID Model diperlukan", "modelDimensionRequired": "Dimensi model dip<PERSON><PERSON><PERSON>", "geminiApiKeyRequired": "Kunci API Gemini diperlukan", "mistralApiKeyRequired": "Kunci API Mistral diperlukan", "ollamaBaseUrlRequired": "URL dasar <PERSON>an", "baseUrlRequired": "URL dasar diperlukan", "modelDimensionMinValue": "Dimensi model harus lebih besar dari 0"}, "advancedConfigLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchMinScoreLabel": "Ambang Batas Skor Pencarian", "searchMinScoreDescription": "<PERSON><PERSON> kesamaan minimum (0.0-1.0) yang diperlukan untuk hasil pencarian. <PERSON><PERSON> yang lebih rendah mengembalikan lebih banyak hasil tetapi mungkin kurang relevan. <PERSON><PERSON> yang lebih tinggi mengembalikan lebih sedikit hasil tetapi lebih relevan.", "searchMinScoreResetTooltip": "Reset ke nilai default (0.4)", "searchMaxResultsLabel": "<PERSON><PERSON>", "searchMaxResultsDescription": "<PERSON><PERSON><PERSON> maksimum hasil pencarian yang dikembalikan saat melakukan query indeks basis kode. <PERSON><PERSON> yang lebih tinggi memberikan lebih banyak konteks tetapi mungkin menyertakan hasil yang kurang relevan.", "resetToDefault": "Reset ke default"}, "autoApprove": {"description": "Izinkan zhanlu untuk secara otomatis melakukan operasi tanpa memerlukan persetujuan. Aktifkan pengaturan ini hanya jika kamu sepenuhnya mempercayai AI dan memahami risiko keamanan yang terkait.", "toggleAriaLabel": "<PERSON><PERSON><PERSON> otomatis", "disabledAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> otomatis din<PERSON> - pilih opsi terlebih dahulu", "readOnly": {"label": "Baca", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan secara otomatis melihat konten direktori dan membaca file tanpa memerlukan kamu mengklik tombol Setujui.", "outsideWorkspace": {"label": "Sertakan file di luar workspace", "description": "Izinkan zhanlu membaca file di luar workspace saat ini tanpa memerlukan persetujuan."}}, "write": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> otomatis membuat dan mengedit file tanpa memerlukan perset<PERSON>an", "delayLabel": "Delay setelah menulis untuk memungkinkan diagnostik mendeteksi masalah potensial", "outsideWorkspace": {"label": "Sertakan file di luar workspace", "description": "Izinkan zhanlu membuat dan mengedit file di luar workspace saat ini tanpa memerlukan persetujuan."}, "protected": {"label": "Sertakan file yang dilindungi", "description": "Izinkan zhanlu membuat dan mengedit file yang dilindungi (seperti .zhanluignore dan file konfigurasi .zhanlu/) tanpa memerlukan persetujuan."}}, "browser": {"label": "Browser", "description": "<PERSON><PERSON>a otomatis melakukan aksi browser tanpa memer<PERSON>an persetu<PERSON>. Catatan: <PERSON>ya berlaku ketika model mendukung computer use"}, "retry": {"label": "<PERSON><PERSON>", "description": "Secara otomatis mencoba ulang permintaan API yang gagal ketika server mengembalikan respons error", "delayLabel": "Delay sebelum mencoba ulang permintaan"}, "mcp": {"label": "MCP", "description": "Aktifkan auto-approval tool MCP individual di tampilan Server MCP (memerlukan pengaturan ini dan checkbox \"Selalu izinkan\" tool tersebut)"}, "modeSwitch": {"label": "Mode", "description": "<PERSON><PERSON><PERSON> otomatis beralih antara mode yang berbeda tanpa memer<PERSON>an perset<PERSON>an"}, "subtasks": {"label": "Subtugas", "description": "Izinkan pembuatan dan penyelesaian subtugas tanpa memerlukan perset<PERSON>an"}, "followupQuestions": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> otomatis memilih jawaban pertama yang disarankan untuk pertanyaan lanjutan setelah batas waktu yang dikonfigurasi", "timeoutLabel": "<PERSON><PERSON><PERSON> tunggu sebelum otomatis memilih jawaban pertama"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Secara otomatis mengeksekusi perintah terminal yang diizinkan tanpa memerlukan persetujuan", "allowedCommands": "Perintah Auto-Execute yang <PERSON>an", "allowedCommandsDescription": "Prefix perintah yang dapat di-auto-execute ketika \"Selalu setujui operasi eksekusi\" diaktifkan. Tambahkan * untuk mengizinkan semua perintah (gunakan dengan hati-hati).", "deniedCommands": "<PERSON><PERSON><PERSON> yang di<PERSON>", "deniedCommandsDescription": "Prefix perintah yang akan ditolak secara otomatis tanpa memerlukan persetujuan. <PERSON><PERSON> kasus konflik dengan perintah yang diizinkan, pencocokan prefix terpanjang diprioritaskan. Tambahkan * untuk menolak semua perintah.", "commandPlaceholder": "Masukkan prefix perintah (misalnya, 'git ')", "deniedCommandPlaceholder": "Masukkan prefix perintah untuk ditolak (misalnya, 'rm -rf')", "addButton": "Tambah", "autoDenied": "<PERSON><PERSON>ah dengan awalan `{{prefix}}` telah dilarang oleh pengguna. <PERSON>an menghindari pembatasan ini dengan menjalankan perintah lain."}, "showMenu": {"label": "Tampilkan menu auto-approve di tampilan chat", "description": "<PERSON><PERSON><PERSON>, menu auto-approve akan ditampilkan di bagian bawah tampilan chat, memungkinkan akses cepat ke pengaturan auto-approve"}, "updateTodoList": {"label": "Todo", "description": "Daftar tugas diperbarui secara otomatis tanpa per<PERSON>an"}, "apiRequestLimit": {"title": "Permin<PERSON><PERSON>", "description": "Secara otomatis membuat sejumlah permintaan API ini sebelum meminta persetujuan untuk melanjutkan tugas.", "unlimited": "Tidak terbatas"}, "selectOptionsFirst": "<PERSON><PERSON>h setidaknya satu opsi di bawah ini untuk mengaktifkan persetujuan otomatis"}, "providers": {"providerDocumentation": "Dokumentasi {{provider}}", "configProfile": "<PERSON><PERSON>", "description": "Simpan konfigurasi API yang berbeda untuk beralih dengan cepat antara provider dan pengaturan.", "apiProvider": "Provider API", "model": "Model", "nameEmpty": "<PERSON>a tidak boleh kosong", "nameExists": "Profil dengan nama ini sudah ada", "deleteProfile": "Hapus Profil", "invalidArnFormat": "Format ARN tidak valid. Silakan periksa contoh di atas.", "enterNewName": "<PERSON><PERSON><PERSON>n nama baru", "addProfile": "Tambah Profil", "renameProfile": "Ganti Nama Profil", "newProfile": "<PERSON>il <PERSON>", "enterProfileName": "<PERSON><PERSON><PERSON><PERSON> nama profil", "createProfile": "Buat Profil", "cannotDeleteOnlyProfile": "Tidak dapat menghapus satu-satunya profil", "searchPlaceholder": "<PERSON><PERSON> profil", "searchProviderPlaceholder": "<PERSON><PERSON>", "noProviderMatchFound": "Tidak ada penyedia di<PERSON>n", "noMatchFound": "Tidak ada profil yang cocok ditemukan", "vscodeLmDescription": " API Model Bahasa VS Code memungkinkan kamu menjalankan model yang disediakan oleh ekstensi VS Code lainnya (termasuk namun tidak terbatas pada GitHub Copilot). Cara termudah untuk memulai adalah menginstal ekstensi Copilot dan <PERSON>lot Chat dari VS Code Marketplace.", "awsCustomArnUse": "Masukkan ARN Amazon Bedrock yang valid untuk model yang ingin kamu gunakan. Contoh format:", "awsCustomArnDesc": "Pastikan region di ARN cocok dengan AWS Region yang kamu pilih di atas.", "openRouterApiKey": "OpenRouter API Key", "getOpenRouterApiKey": "Dapatkan OpenRouter API Key", "apiKeyStorageNotice": "API key disimpan dengan aman di Secret Storage VSCode", "glamaApiKey": "Glama API Key", "getGlamaApiKey": "Dapatkan Glama API Key", "useCustomBaseUrl": "Gunakan base URL kustom", "useReasoning": "Aktifkan reasoning", "useHostHeader": "Gunakan Host header kustom", "useLegacyFormat": "Gunakan format API OpenAI legacy", "customHeaders": "Header <PERSON>", "headerName": "<PERSON><PERSON> header", "headerValue": "<PERSON><PERSON> header", "noCustomHeaders": "Tidak ada header kustom yang didef<PERSON>. Klik tombol + untuk menambahkan satu.", "requestyApiKey": "Requesty API Key", "refreshModels": {"label": "Refresh Model", "hint": "<PERSON><PERSON><PERSON> buka kembali pengaturan untuk melihat model terb<PERSON>.", "loading": "Merefresh daftar model...", "success": "Daftar model berhasil direfresh!", "error": "<PERSON><PERSON> merefresh daftar model. Silakan coba lagi."}, "getRequestyApiKey": "Dapatkan Requesty API Key", "openRouterTransformsText": "Kompres prompt dan rantai pesan ke ukuran konteks (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Anthropic API Key", "getAnthropicApiKey": "Dapatkan Anthropic API Key", "anthropicUseAuthToken": "Kirim Anthropic API Key sebagai Authorization header alih-alih X-Api-Key", "chutesApiKey": "Chutes API Key", "getChutesApiKey": "Dapatkan Chutes API Key", "deepSeekApiKey": "DeepSeek API Key", "getDeepSeekApiKey": "Dapatkan DeepSeek API Key", "moonshotApiKey": "Kunci API Moonshot", "getMoonshotApiKey": "Dapatkan Kunci API Moonshot", "moonshotBaseUrl": "<PERSON>itik <PERSON>", "geminiApiKey": "Gemini API Key", "getGroqApiKey": "Dapatkan Groq API Key", "groqApiKey": "Groq API Key", "getHuggingFaceApiKey": "Dapatkan Kunci API Hugging Face", "huggingFaceApiKey": "Kunci API Hugging Face", "huggingFaceModelId": "ID Model", "getGeminiApiKey": "Dapatkan Gemini API Key", "huggingFaceLoading": "Memuat...", "huggingFaceModelsCount": "({{count}} model)", "huggingFaceSelectModel": "<PERSON><PERSON><PERSON> model...", "huggingFaceSearchModels": "Cari model...", "huggingFaceNoModelsFound": "Tidak ada model di<PERSON><PERSON>n", "huggingFaceProvider": "Penyedia", "huggingFaceProviderAuto": "<PERSON><PERSON><PERSON><PERSON>", "huggingFaceSelectProvider": "<PERSON><PERSON><PERSON>...", "huggingFaceSearchProviders": "<PERSON>i pen<PERSON>...", "huggingFaceNoProvidersFound": "Tidak ada penyedia di<PERSON>n", "openAiApiKey": "OpenAI API Key", "apiKey": "API Key", "openAiBaseUrl": "Base URL", "getOpenAiApiKey": "Dapatkan OpenAI API Key", "mistralApiKey": "Mistral API Key", "getMistralApiKey": "Dapatkan Mistral / Codestral API Key", "codestralBaseUrl": "Codestral Base URL (Opsional)", "codestralBaseUrlDesc": "Atur URL alternatif untuk model Codestral.", "xaiApiKey": "xAI API Key", "getXaiApiKey": "Dapatkan xAI API Key", "litellmApiKey": "LiteLLM API Key", "litellmBaseUrl": "LiteLLM Base URL", "awsCredentials": "AWS Credentials", "awsProfile": "AWS Profile", "awsApiKey": "Kunci API Amazon Bedrock", "awsProfileName": "Nama AWS Profile", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsSessionToken": "AWS Session Token", "awsRegion": "AWS Region", "awsCrossRegion": "Gunakan cross-region inference", "awsBedrockVpc": {"useCustomVpcEndpoint": "Gunakan VPC endpoint kustom", "vpcEndpointUrlPlaceholder": "Masukkan VPC Endpoint URL (opsional)", "examples": "Contoh:"}, "enablePromptCaching": "Aktifkan prompt caching", "enablePromptCachingTitle": "Aktifkan prompt caching untuk meningkatkan performa dan mengurangi biaya untuk model yang didukung.", "cacheUsageNote": "Catatan: <PERSON><PERSON> kamu tidak melihat penggunaan cache, coba pilih model yang berbeda lalu pilih model yang kamu inginkan lagi.", "vscodeLmModel": "Model Bahasa", "vscodeLmWarning": "Catatan: <PERSON>i adalah integrasi yang sangat eksperimental dan dukungan provider akan bervariasi. <PERSON><PERSON> kamu mendapat error tentang model yang tidak didukung, itu adalah masalah di sisi provider.", "googleCloudSetup": {"title": "Untuk menggunakan Google Cloud Vertex AI, kamu perlu:", "step1": "1. <PERSON><PERSON>t akun Google Cloud, aktifkan Vertex AI API & aktifkan model <PERSON> ya<PERSON>.", "step2": "2. Instal Google Cloud CLI & konfigurasi application default credentials.", "step3": "3. Atau buat service account dengan credentials."}, "googleCloudCredentials": "Google Cloud Credentials", "googleCloudKeyFile": "Path File Key Google Cloud", "googleCloudProjectId": "Google Cloud Project ID", "googleCloudRegion": "Google Cloud Region", "lmStudio": {"baseUrl": "Base URL (opsional)", "modelId": "Model ID", "speculativeDecoding": "Aktifkan Speculative Decoding", "draftModelId": "Draft Model ID", "draftModelDesc": "Draft model harus dari keluarga model yang sama agar speculative decoding bekerja dengan benar.", "selectDraftModel": "<PERSON><PERSON><PERSON> Draft Model", "noModelsFound": "Tidak ada draft model di<PERSON><PERSON><PERSON>. Pastikan LM Studio berjalan dengan Server Mode diaktifkan.", "description": "LM Studio memungkinkan kamu menjalankan model secara lokal di komputer. Untuk instruksi cara memulai, lihat <a>panduan quickstart</a> mereka. Kamu juga perlu memulai fitur <b>local server</b> LM Studio untuk menggunakannya dengan ekstensi ini. <span>Catatan:</span> zhanlu menggunakan prompt kompleks dan bekerja terbaik dengan model <PERSON>. Model yang kurang mampu mungkin tidak bekerja seperti yang diharapkan."}, "ollama": {"baseUrl": "Base URL (opsional)", "modelId": "Model ID", "description": "<PERSON><PERSON><PERSON> memungkinkan kamu <PERSON> model secara lokal di komputer. Untuk instruksi cara memulai, lihat panduan quickstart mereka.", "warning": "Catatan: <PERSON><PERSON><PERSON> prompt kompleks dan bekerja terbaik dengan model <PERSON>. Model yang kurang mampu mungkin tidak bekerja seperti yang diharapkan."}, "unboundApiKey": "Unbound API Key", "getUnboundApiKey": "Dapatkan Unbound API Key", "unboundRefreshModelsSuccess": "Daftar model dip<PERSON><PERSON><PERSON>! Kamu sekarang dapat memilih dari model terb<PERSON>.", "unboundInvalidApiKey": "API key tidak valid. Silakan periksa API key kamu dan coba lagi.", "humanRelay": {"description": "Tidak diperlukan API key, tetapi pengguna perlu membantu menyalin dan menempel informasi ke web chat AI.", "instructions": "<PERSON><PERSON><PERSON>, kotak dialog akan muncul dan pesan saat ini akan disalin ke clipboard secara otomatis. Kamu perlu menempel ini ke versi web AI (seperti ChatGPT atau Claude), lalu salin balasan AI kembali ke kotak dialog dan klik tombol konfirmasi."}, "openRouter": {"providerRouting": {"title": "OpenRouter Provider Routing", "description": "OpenRouter mengarahkan permintaan ke provider terbaik yang tersedia untuk model kamu. Secara default, permintaan diseimbangkan beban di seluruh provider teratas untuk memaksimalkan uptime. Namun, kamu dapat memilih provider spesifik untuk digunakan untuk model ini.", "learnMore": "Pelajari lebih lanjut tentang provider routing"}}, "customModel": {"capabilities": "Kon<PERSON><PERSON><PERSON><PERSON> kemampuan dan harga untuk model kustom yang kompatibel dengan OpenAI. Hati-hati saat menentukan kemampuan model, karena dapat memp<PERSON><PERSON><PERSON> performa z<PERSON>.", "maxTokens": {"label": "Token Output Maksimum", "description": "<PERSON><PERSON><PERSON> maksimum token yang dapat dihasilkan model dalam respons. (Tentukan -1 untuk membiarkan server mengatur token maksimum.)"}, "contextWindow": {"label": "Ukuran Context Window", "description": "Total token (input + output) yang dapat diproses model."}, "imageSupport": {"label": "Dukungan Gambar", "description": "<PERSON><PERSON><PERSON>h model ini mampu memproses dan memahami gambar?"}, "computerUse": {"label": "Computer Use", "description": "Apakah model ini mampu berinteraksi dengan browser? (mi<PERSON><PERSON> Claude 3.7 Sonnet)."}, "promptCache": {"label": "Prompt Caching", "description": "Apakah model ini mampu melakukan caching prompt?"}, "pricing": {"input": {"label": "<PERSON><PERSON>", "description": "Biaya per juta token dalam input/prompt. Ini mempengaruhi biaya mengirim konteks dan instruksi ke model."}, "output": {"label": "<PERSON><PERSON>", "description": "Biaya per juta token dalam respons model. Ini mempengaruhi biaya konten yang dihasilkan dan completion."}, "cacheReads": {"label": "<PERSON><PERSON>", "description": "Biaya per juta token untuk membaca dari cache. Ini adalah harga yang dikenakan ketika respons yang di-cache diambil."}, "cacheWrites": {"label": "<PERSON><PERSON>", "description": "Biaya per juta token untuk menulis ke cache. Ini adalah harga yang dikenakan ketika prompt di-cache untuk pertama kalinya."}}, "resetDefaults": "Reset ke Default"}, "rateLimitSeconds": {"label": "Rate limit", "description": "Waktu minimum antara permintaan API."}, "consecutiveMistakeLimit": {"label": "Batas Kesalahan & Pengulangan", "description": "<PERSON><PERSON><PERSON> kes<PERSON>han berturut-turut atau tindakan berulang sebelum menampilkan dialog '<PERSON><PERSON><PERSON> mengalami masalah'", "unlimitedDescription": "Percobaan ulang tak terbatas diaktifkan (lanjut otomatis). Dialog tidak akan pernah muncul.", "warning": "⚠️ Mengatur ke 0 memungkinkan percobaan ulang tak terbatas yang dapat menghabiskan penggunaan API yang signifikan"}, "reasoningEffort": {"label": "Upaya Reasoning Model", "high": "Tingg<PERSON>", "medium": "Sedang", "low": "Rendah"}, "setReasoningLevel": "Aktifkan Upaya Reasoning", "claudeCode": {"pathLabel": "<PERSON><PERSON><PERSON>", "description": "Jalur opsional ke Claude Code CLI Anda. Defaultnya adalah 'claude' jika tidak diatur.", "placeholder": "Default: claude", "maxTokensLabel": "Token Output Maks", "maxTokensDescription": "<PERSON><PERSON><PERSON> maksimum token output untuk respons Claude Code. Default adalah 8000."}}, "browser": {"enable": {"label": "Aktifkan tool browser", "description": "<PERSON><PERSON><PERSON> diaktifkan, <PERSON><PERSON><PERSON> dapat menggunakan browser untuk berinteraksi dengan website ketika menggunakan model yang mendukung computer use. <0>Pelajari lebih lanjut</0>"}, "viewport": {"label": "Ukuran viewport", "description": "Pilih ukuran viewport untuk interaksi browser. Ini mempengaruhi bagaimana website ditampilkan dan berinteraksi.", "options": {"largeDesktop": "Desktop Besar (1280x800)", "smallDesktop": "Desktop Kecil (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobile (360x640)"}}, "screenshotQuality": {"label": "<PERSON><PERSON><PERSON> screenshot", "description": "Sesuaikan kualitas WebP screenshot browser. <PERSON><PERSON> yang lebih tinggi memberikan screenshot yang lebih jelas tetapi meningkatkan penggunaan token."}, "remote": {"label": "Gunakan koneksi browser remote", "description": "Hubungkan ke browser Chrome yang berjalan dengan remote debugging diaktifkan (--remote-debugging-port=9222).", "urlPlaceholder": "URL Kustom (misalnya, http://localhost:9222)", "testButton": "Test Koneksi", "testingButton": "Testing...", "instructions": "<PERSON><PERSON><PERSON><PERSON> al<PERSON><PERSON> host DevTools Protocol atau biarkan kosong untuk auto-discover instance Chrome lokal. Tombol Test Koneksi akan mencoba URL kustom jika disediakan, atau auto-discover jika field kosong."}}, "checkpoints": {"enable": {"label": "Aktifkan checkpoint otomatis", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan secara otomatis membuat checkpoint selama e<PERSON><PERSON><PERSON> tugas, memudahkan untuk meninjau perubahan atau kembali ke state sebelumnya. <0>Pelajari lebih lanjut</0>"}}, "notifications": {"sound": {"label": "Aktifkan efek suara", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan memutar efek suara untuk notifikasi dan event.", "volumeLabel": "Volume"}, "tts": {"label": "Aktifkan text-to-speech", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan membacakan responnya menggunakan text-to-speech.", "speedLabel": "Kecepatan"}}, "contextManagement": {"description": "Kontrol informasi apa yang disertakan dalam context window AI, mempengaruhi penggunaan token dan kualitas respons", "autoCondenseContextPercent": {"label": "Ambang batas untuk memicu kondensasi konteks cerdas", "description": "Ketika context window mencapai ambang batas ini, <PERSON><PERSON><PERSON> akan secara otomatis mengondensasikannya."}, "condensingApiConfiguration": {"label": "Konfigurasi API untuk Kondensasi Konteks", "description": "Pilih konfigurasi API mana yang akan digunakan untuk operasi kondensasi konteks. Biarkan tidak dipilih untuk menggunakan konfigurasi aktif saat ini.", "useCurrentConfig": "<PERSON><PERSON><PERSON>"}, "customCondensingPrompt": {"label": "Prompt Kondensasi Konteks Kustom", "description": "Kustomisasi system prompt yang digunakan untuk kondensasi konteks. Biarkan kosong untuk menggunakan prompt default.", "placeholder": "Masukkan prompt kondensasi kustom kamu di sini...\n\n<PERSON><PERSON> dapat menggunakan struktur yang sama dengan prompt default:\n- Previous Conversation\n- Current Work\n- Key Technical Concepts\n- Relevant Files and Code\n- Problem Solving\n- Pending Tasks and Next Steps", "reset": "Reset ke Default", "hint": "Kosong = gunakan prompt default"}, "autoCondenseContext": {"name": "<PERSON><PERSON><PERSON> otomatis memicu kondensasi konteks cerdas", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan secara otomatis mengondensasi konteks ketika ambang batas tercapai. <PERSON><PERSON><PERSON> din<PERSON>, kamu ma<PERSON>h dapat memicu kondensasi konteks secara manual."}, "diagnostics": {"includeMessages": {"label": "<PERSON><PERSON><PERSON> otomatis sertakan diagnostik dalam konteks", "description": "<PERSON><PERSON><PERSON> diak<PERSON>, pesan diagnostik (error) dari file yang diedit akan secara otomatis disertakan dalam konteks. Kamu selalu dapat menyertakan semua diagnostik workspace secara manual menggunakan @problems."}, "maxMessages": {"label": "Pesan diagnostik maksimum", "description": "<PERSON><PERSON><PERSON> maksimum pesan diagnostik yang akan disertakan per file. Batas ini berlaku untuk penyertaan otomatis (ketika checkbox diaktifkan) dan penyebutan manual @problems. <PERSON><PERSON> yang lebih tinggi memberikan lebih banyak konteks tetapi meningkatkan penggunaan token.", "resetTooltip": "Reset ke nilai default (50)", "unlimited": "Pesan diagnostik tidak terbatas", "unlimitedLabel": "Tak terbatas"}, "delayAfterWrite": {"label": "Tunda setelah penulisan untuk memungkinkan diagnostik mendeteksi potensi masalah", "description": "<PERSON><PERSON><PERSON> tunggu setelah penulisan file sebelum melanjutkan, memungkinkan alat diagnostik untuk memproses perubahan dan mendeteksi masalah."}}, "condensingThreshold": {"label": "Ambang <PERSON>", "selectProfile": "Konfigurasi ambang batas untuk profil", "defaultProfile": "Default Global (semua profil)", "defaultDescription": "Ketika konteks mencapai persentase ini, akan secara otomatis dikondensasi untuk semua profil kecuali mereka memiliki pengaturan kustom", "profileDescription": "Ambang batas kustom untuk profil ini saja (menimpa default global)", "inheritDescription": "Profil ini mewarisi ambang batas default global ({{threshold}}%)", "usesGlobal": "(menggunakan global {{threshold}}%)"}, "openTabs": {"label": "Batas konteks tab terbuka", "description": "<PERSON><PERSON><PERSON> maksimum tab VSCode terbuka yang disertakan dalam konteks. <PERSON><PERSON> yang lebih tinggi memberikan lebih banyak konteks tetapi meningkatkan penggunaan token."}, "workspaceFiles": {"label": "Batas konteks file workspace", "description": "<PERSON><PERSON><PERSON> maksimum file yang disertakan dalam detail direktori kerja saat ini. <PERSON><PERSON> yang lebih tinggi memberikan lebih banyak konteks tetapi meningkatkan penggunaan token."}, "rooignore": {"label": "Tampilkan file .z<PERSON><PERSON><PERSON><PERSON>'d dalam daftar dan pencarian", "description": "<PERSON><PERSON><PERSON> di<PERSON>, file yang cocok dengan pola di .zhanluignore akan ditampilkan dalam daftar dengan simbol kunci. <PERSON><PERSON><PERSON> dinonaktif<PERSON>, file ini akan sepenuhnya disembunyikan dari daftar file dan pencarian."}, "maxConcurrentFileReads": {"label": "Batas pembacaan file bersamaan", "description": "Ju<PERSON><PERSON> maksimum file yang dapat diproses oleh tool 'read_file' secara bersa<PERSON>. <PERSON>lai yang lebih tinggi dapat mempercepat pembacaan beberapa file kecil tetapi meningkatkan penggunaan memori."}, "maxReadFile": {"label": "Ambang batas auto-truncate pembacaan file", "description": "zhanlu membaca sejumlah baris ini ketika model menghilangkan nilai start/end. Jika angka ini kurang dari total file, zhan<PERSON> menghasilkan indeks nomor baris dari definisi kode. Kasus khusus: -1 menginstruksikan zhanlu untuk membaca seluruh file (tanpa indexing), dan 0 menginstruksikannya untuk tidak membaca baris dan hanya menyediakan indeks baris untuk konteks minimal. <PERSON><PERSON> yang lebih rendah meminimalkan penggunaan konteks awal, memungkinkan pembacaan rentang baris yang tepat selanjutnya. Permintaan start/end eksplisit tidak dibatasi oleh pengaturan ini.", "lines": "baris", "always_full_read": "<PERSON><PERSON><PERSON> baca seluruh file"}}, "terminal": {"basic": {"label": "Pengaturan Terminal: Dasar", "description": "Pengaturan terminal dasar"}, "advanced": {"label": "Pengaturan Terminal: Lanjutan", "description": "Opsi berikut mungkin memerlukan restart terminal untuk menerapkan pengaturan."}, "outputLineLimit": {"label": "Batas output terminal", "description": "<PERSON><PERSON><PERSON> maksimum baris yang disertakan dalam output terminal saat mengeksekusi perintah. <PERSON><PERSON><PERSON> te<PERSON>, baris akan dihapus dari tengah, mengh<PERSON><PERSON> token. <0>Pelajari lebih lanjut</0>"}, "outputCharacterLimit": {"label": "Batas karakter terminal", "description": "<PERSON><PERSON><PERSON> maksimum karakter yang akan disertakan dalam output terminal saat menjalankan perintah. Batas ini lebih diutamakan daripada batas baris untuk mencegah masalah memori dari baris yang sangat panjang. Ketika terlampaui, output akan dipotong. <0>Pelajari lebih lanjut</0>"}, "shellIntegrationTimeout": {"label": "Timeout integrasi shell terminal", "description": "Waktu maksimum untuk menunggu integrasi shell menginisialisasi sebelum mengeksekusi perintah. Untuk pengguna dengan waktu startup shell yang lama, nilai ini mungkin perlu ditingkatkan jika kamu melihat error \"Shell Integration Unavailable\" di terminal. <0>Pelajari lebih lanjut</0>"}, "shellIntegrationDisabled": {"label": "Nonaktifkan integrasi shell terminal", "description": "Aktifkan ini jika perintah terminal tidak bekerja dengan benar atau kamu melihat error 'Shell Integration Unavailable'. Ini menggunakan metode yang lebih sederhana untuk menjalankan perintah, melewati beberapa fitur terminal lanjutan. <0>Pelajari lebih lanjut</0>"}, "commandDelay": {"label": "Delay perintah terminal", "description": "Delay dalam milidetik untuk ditambahkan setelah eksekusi perintah. Pengaturan default 0 menonaktifkan delay sepenuhnya. Ini dapat membantu memastikan output perintah sepenuhnya ditangkap di terminal dengan masalah timing. Di sebagian besar terminal ini diimplementasikan dengan mengatur `PROMPT_COMMAND='sleep N'` dan <PERSON>hell menambahkan `start-sleep` di akhir setiap perintah. Awalnya adalah workaround untuk VSCode bug#237208 dan mungkin tidak diperlukan. <0>Pelajari lebih lanjut</0>"}, "compressProgressBar": {"label": "Kompres output progress bar", "description": "Ketika diaktifkan, memproses output terminal dengan carriage return (\\r) untuk mensimulasikan bagaimana terminal nyata akan menampilkan konten. Ini menghapus state progress bar intermediate, hanya mempertahankan state final, yang menghemat ruang konteks untuk informasi yang lebih relevan. <0>Pelajari lebih lanjut</0>"}, "powershellCounter": {"label": "Aktifkan workaround counter PowerShell", "description": "Ke<PERSON>ka diaktifkan, menambahkan counter ke perintah PowerShell untuk memastikan eksekusi perintah yang tepat. Ini membantu dengan terminal PowerShell yang mungkin memiliki masalah dengan penangkapan output perintah. <0>Pelajari lebih lanjut</0>"}, "zshClearEolMark": {"label": "Hapus ZSH EOL mark", "description": "<PERSON><PERSON><PERSON>, menghapus ZSH end-of-line mark dengan mengatur PROMPT_EOL_MARK=''. Ini mencegah masalah dengan interpretasi output perintah ketika output berakhir dengan karakter khusus seperti '%'. <0>Pelajari lebih lanjut</0>"}, "zshOhMy": {"label": "Aktifkan integrasi Oh My Zsh", "description": "<PERSON><PERSON><PERSON> di<PERSON>, mengatur ITERM_SHELL_INTEGRATION_INSTALLED=Yes untuk mengaktifkan fitur integrasi shell Oh My Zsh. Menerapkan pengaturan ini mungkin memerlukan restart IDE. <0>Pelajari lebih lanjut</0>"}, "zshP10k": {"label": "Aktifkan integrasi Powerlevel10k", "description": "<PERSON><PERSON><PERSON>, mengatur POWERLEVEL9K_TERM_SHELL_INTEGRATION=true untuk mengaktifkan fitur integrasi shell Powerlevel10k. <0><PERSON><PERSON>jar<PERSON> lebih lanjut</0>"}, "zdotdir": {"label": "Aktifkan penanganan ZDOTDIR", "description": "<PERSON><PERSON><PERSON> di<PERSON>, membuat direktori sementara untuk ZDOTDIR untuk menangani integrasi shell zsh dengan benar. Ini memastikan integrasi shell VSCode bekerja dengan benar dengan zsh sambil mempertahankan konfigurasi zsh kamu. <0>Pelajari lebih lanjut</0>"}, "inheritEnv": {"label": "Warisi variabel environment", "description": "<PERSON><PERSON><PERSON>, terminal akan mewarisi variabel environment dari proses parent VSCode, seperti pengaturan integrasi shell yang didefinisikan user-profile. Ini secara langsung mengalihkan pengaturan global VSCode `terminal.integrated.inheritEnv`. <0>P<PERSON>jari lebih lanjut</0>"}}, "advancedSettings": {"title": "Pengaturan lanjutan"}, "advanced": {"diff": {"label": "Aktifkan editing melalui diff", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan dapat mengedit file lebih cepat dan akan secara otomatis menolak penulisan file penuh yang terpotong. Bekerja terbaik dengan model Claude 3.7 Sonnet terbaru.", "strategy": {"label": "Strategi diff", "options": {"standard": "Standard (Single block)", "multiBlock": "Eksperimental: Multi-block diff", "unified": "Eksperimental: Unified diff"}, "descriptions": {"standard": "Strategi diff standard menerapkan perubahan ke satu blok kode pada satu waktu.", "unified": "Strategi unified diff mengambil beberapa pendekatan untuk menerapkan diff dan memilih pendekatan terbaik.", "multiBlock": "Strategi multi-block diff memungkinkan memperbarui beberapa blok kode dalam file dalam satu permintaan."}}, "matchPrecision": {"label": "Presisi <PERSON>kan", "description": "Slider ini mengontrol seberapa tepat bagian kode harus cocok saat menerapkan diff. <PERSON><PERSON> yang lebih rendah memungkinkan pencocokan yang lebih fleksibel tetapi meningkatkan risiko penggantian yang salah. Gunakan nilai di bawah 100% dengan sangat hati-hati."}}, "todoList": {"label": "Aktifkan alat daftar tugas", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dapat membuat dan mengelola daftar tugas untuk melacak kemajuan tugas. Ini membantu mengatur tugas kompleks menjadi langkah-langkah yang dapat dikelola."}}, "completion": {"description": "Konfigurasikan pengaturan penyelesaian kode untuk meningkatkan pengalaman pengembangan Anda.", "configureButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debounceTime": {"label": "<PERSON><PERSON><PERSON> pemicu pen<PERSON>", "description": "<PERSON><PERSON><PERSON> penundaan untuk pemicu penyelesaian kode (milidetik)"}, "number": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kandidat penye<PERSON>aian kode yang akan dihas<PERSON>kan"}, "granularity": {"label": "Preferensi granularitas pen<PERSON>lesaian", "description": "Pengaturan preferensi granularitas untuk penyelesaian kode", "singleRow": "<PERSON><PERSON> t<PERSON>", "oneTimeMaximization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balanced": "Seimbang"}, "multipleLineCompletion": {"label": "Mode penyelesaian multi-baris", "description": "Mode pemicu untuk penyelesaian kode multi-baris", "autoCompletion": "Pen<PERSON>lesaia<PERSON> otomatis", "triggerCompletion": "<PERSON><PERSON><PERSON><PERSON><PERSON> pemicu"}, "maxTokens": {"label": "Token maksimum", "description": "<PERSON><PERSON><PERSON> maksimum token untuk penyelesaian kode"}}, "experimental": {"warning": "⚠️", "autoCondenseContextPercent": {"label": "Ambang batas untuk memicu kondensasi konteks cerdas", "description": "Ketika context window mencapai ambang batas ini, <PERSON><PERSON><PERSON> akan secara otomatis mengondensasikannya."}, "condensingApiConfiguration": {"label": "Konfigurasi API untuk Kondensasi Konteks", "description": "Pilih konfigurasi API mana yang akan digunakan untuk operasi kondensasi konteks. Biarkan tidak dipilih untuk menggunakan konfigurasi aktif saat ini.", "useCurrentConfig": "<PERSON><PERSON><PERSON>"}, "customCondensingPrompt": {"label": "Prompt Kondensasi Konteks Kustom", "description": "Kustomisasi system prompt yang digunakan untuk kondensasi konteks. Biarkan kosong untuk menggunakan prompt default.", "placeholder": "Masukkan prompt kondensasi kustom kamu di sini...\n\n<PERSON><PERSON> dapat menggunakan struktur yang sama dengan prompt default:\n- Previous Conversation\n- Current Work\n- Key Technical Concepts\n- Relevant Files and Code\n- Problem Solving\n- Pending Tasks and Next Steps", "reset": "Reset ke Default", "hint": "Kosong = gunakan prompt default"}, "AUTO_CONDENSE_CONTEXT": {"name": "Secara cerdas mengondensasi context window", "description": "Kondensasi konteks cerdas menggunakan panggilan LLM untuk merangkum percakapan masa lalu ketika context window tugas mencapai ambang batas yang telah ditetapkan, da<PERSON><PERSON> menghapus pesan lama ketika konteks penuh."}, "DIFF_STRATEGY_UNIFIED": {"name": "<PERSON><PERSON><PERSON> strategi unified diff eksperimental", "description": "Aktifkan strategi unified diff eksperimental. Strategi ini mungkin mengurangi jumlah retry yang disebabkan oleh error model tetapi dapat menyebabkan perilaku yang tidak terduga atau edit yang salah. Hanya aktifkan jika kamu memahami risikonya dan bersedia meninjau semua perubahan dengan hati-hati."}, "SEARCH_AND_REPLACE": {"name": "Gunakan tool search and replace eksperimental", "description": "Aktifkan tool search and replace eksperimental, memungkinkan zhanlu mengganti beberapa instance dari term pencarian dalam satu permintaan."}, "INSERT_BLOCK": {"name": "Gunakan tool insert content eksperimental", "description": "Aktifkan tool insert content eksperimental, memungkinkan zhanlu menyisipkan konten pada nomor baris spesifik tanpa perlu membuat diff."}, "POWER_STEERING": {"name": "Gunakan mode \"power steering\" eksperimental", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> akan menging<PERSON>kan model tentang detail definisi mode saat ini lebih sering. Ini akan menghasilkan kepatuhan yang lebih kuat terhadap definisi peran dan instruksi kustom, tetapi akan menggunakan lebih banyak token per pesan."}, "AUTOCOMPLETE": {"name": "<PERSON><PERSON>n fitur \"autocomplete\" eksperimental", "description": "<PERSON><PERSON><PERSON>, zhan<PERSON> akan memberikan saran kode inline saat kamu mengetik. Memerlukan zhanlu API Provider."}, "CONCURRENT_FILE_READS": {"name": "Aktifkan pembacaan file bersamaan", "description": "<PERSON><PERSON><PERSON> di<PERSON>, zhanlu dapat membaca beberapa file dalam satu permintaan. Ketika dinonaktifkan, zhanlu harus membaca file satu per satu. Menonaktifkan ini dapat membantu saat bekerja dengan model yang kurang mampu atau ketika kamu ingin kontrol lebih terhadap akses file."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Gunakan tool multi block diff eksperimental", "description": "<PERSON><PERSON><PERSON> di<PERSON>, <PERSON><PERSON><PERSON> akan menggunakan tool multi block diff. Ini akan mencoba memperbarui beberapa blok kode dalam file dalam satu permintaan."}, "MARKETPLACE": {"name": "Aktifkan Marketplace", "description": "<PERSON><PERSON><PERSON>, kamu da<PERSON><PERSON> men<PERSON>tal MCP dan mode kustom dari Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Aktifkan edit file bersamaan", "description": "<PERSON><PERSON><PERSON> di<PERSON>, zhan<PERSON> dapat mengedit beberapa file dalam satu permintaan. <PERSON><PERSON><PERSON> dinonaktif<PERSON>, zhanlu harus mengedit file satu per satu. Menonaktifkan ini dapat membantu saat bekerja dengan model yang kurang mampu atau ketika kamu ingin kontrol lebih terhadap modifikasi file."}}, "promptCaching": {"label": "Nonaktifkan prompt caching", "description": "<PERSON><PERSON><PERSON> dice<PERSON>, <PERSON><PERSON><PERSON> tidak akan menggunakan prompt caching untuk model ini."}, "temperature": {"useCustom": "Gunakan temperature kustom", "description": "Mengontrol keacakan dalam respons model.", "rangeDescription": "Nilai yang lebih tinggi membuat output lebih acak, nilai yang lebih rendah membuatnya lebih deterministik."}, "modelInfo": {"supportsImages": "Mendukung gambar", "noImages": "Tidak mendukung gambar", "supportsComputerUse": "Mendukung computer use", "noComputerUse": "Tidak mendukung computer use", "supportsPromptCache": "Men<PERSON><PERSON>ng prompt caching", "noPromptCache": "Tidak mendukung prompt caching", "maxOutput": "Output maksimum", "inputPrice": "<PERSON><PERSON> input", "outputPrice": "Harga output", "cacheReadsPrice": "Harga cache reads", "cacheWritesPrice": "<PERSON><PERSON> <PERSON> writes", "enableStreaming": "Aktifkan streaming", "enableR1Format": "Aktifkan parameter model R1", "enableR1FormatTips": "Harus diaktifkan saat menggunakan model R1 seperti QWQ untuk mencegah error 400", "useAzure": "Gunakan Azure", "azureApiVersion": "Atur versi API Azure", "gemini": {"freeRequests": "* G<PERSON><PERSON> hingga {{count}} permintaan per menit. <PERSON><PERSON><PERSON> itu, pen<PERSON>han tergantung pada ukuran prompt.", "pricingDetails": "Untuk info lebih lanjut, lihat detail harga.", "billingEstimate": "* <PERSON><PERSON><PERSON> adalah estimasi - biaya sebenarnya tergantung pada ukuran prompt."}}, "modelPicker": {"automaticFetch": "Ekstensi secara otomatis mengambil daftar model terbaru yang tersedia di <serviceLink>{{serviceName}}</serviceLink>. <PERSON>ka kamu tidak yakin model mana yang harus dipilih, <PERSON><PERSON><PERSON> bekerja terbaik dengan <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Kamu juga dapat mencoba mencari \"free\" untuk opsi tanpa biaya yang saat ini tersedia.", "label": "Model", "searchPlaceholder": "<PERSON><PERSON>", "noMatchFound": "Tidak ada yang cocok ditemukan", "useCustomModel": "<PERSON><PERSON><PERSON> kustom: {{modelId}}"}, "footer": {"feedback": "<PERSON>ka kamu punya per<PERSON>aan atau feedback, jangan ragu untuk membuka issue di <githubLink>github.com/RooCodeInc/Roo-Code</githubLink> atau bergabung <redditLink>reddit.com/r/RooCode</redditLink> atau <discordLink>discord.gg/roocode</discordLink>", "telemetry": {"label": "Izinkan pelaporan error dan penggunaan anonim", "description": "Bantu tingkatkan zhanlu dengan mengirim data penggunaan anonim dan laporan error. <PERSON><PERSON>k ada kode, prompt, atau informasi pribadi yang pernah dikirim. Lihat kebijakan privasi kami untuk detail lebih lanjut."}, "settings": {"import": "Impor", "export": "Ekspor", "reset": "Reset"}}, "thinkingBudget": {"maxTokens": "<PERSON><PERSON>", "maxThinkingTokens": "Token <PERSON> Maksimum"}, "validation": {"apiKey": "Kamu harus menyediakan API key yang valid.", "awsRegion": "Kamu harus memilih region untuk digunakan dengan Amazon Bedrock.", "googleCloud": "Kamu harus menyediakan Google Cloud Project ID dan Region yang valid.", "modelId": "Kamu harus menyediakan model ID yang valid.", "modelSelector": "Kamu harus menyediakan model selector yang valid.", "openAi": "<PERSON>mu harus menyediakan base URL, API key, dan model ID yang valid.", "arn": {"invalidFormat": "Format ARN tidak valid. Silakan periksa persyaratan format.", "regionMismatch": "Peringatan: Region di ARN kamu ({{arnRegion}}) tidak cocok dengan region yang kamu pilih ({{region}}). Ini dapat menyebabkan masalah akses. Provider akan menggunakan region dari ARN."}, "modelAvailability": "Model ID ({{modelId}}) yang kamu berikan tidak tersedia. <PERSON>lakan pilih model yang berbeda.", "providerNotAllowed": "Provider '{{provider}}' tidak diizinkan oleh organisasi kamu", "modelNotAllowed": "Model '{{model}}' tidak diizinkan untuk provider '{{provider}}' oleh organisasi kamu", "profileInvalid": "Profil ini berisi provider atau model yang tidak diizinkan oleh organisasi kamu"}, "placeholders": {"apiKey": "Masukkan API Key...", "profileName": "<PERSON><PERSON><PERSON><PERSON> nama profil", "accessKey": "Masukkan Access Key...", "secretKey": "Masukkan Secret Key...", "sessionToken": "Masukkan Session Token...", "credentialsJson": "Masukkan Credentials JSON...", "keyFilePath": "Masukkan Path File Key...", "projectId": "Masukkan Project ID...", "customArn": "Masukkan ARN (misalnya arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Masukkan base URL...", "modelId": {"lmStudio": "misalnya meta-llama-3.1-8b-instruct", "lmStudioDraft": "misalnya lmstudio-community/llama-3.2-1b-instruct", "ollama": "misalnya llama3.1"}, "numbers": {"maxTokens": "misalnya 4096", "contextWindow": "misalnya 128000", "inputPrice": "misalnya 0.0001", "outputPrice": "misalnya 0.0002", "cacheWritePrice": "misalnya 0.00005"}}, "defaults": {"ollamaUrl": "Default: http://localhost:11434", "lmStudioUrl": "Default: http://localhost:1234", "geminiUrl": "Default: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN Kustom", "useCustomArn": "Gunakan ARN kustom..."}, "includeMaxOutputTokens": "Sertakan token output maksimum", "includeMaxOutputTokensDescription": "Kirim parameter token output maksimum dalam permintaan API. Beberapa provider mungkin tidak mendukung ini.", "limitMaxTokensDescription": "<PERSON><PERSON> jumlah maksimum token dalam respons", "maxOutputTokensLabel": "Token output maksimum", "maxTokensGenerateDescription": "Token maksimum untuk dihasilkan dalam respons"}