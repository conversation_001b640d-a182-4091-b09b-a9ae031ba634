{"greeting": "Selamat datang di <PERSON>lu", "task": {"title": "Tugas", "seeMore": "<PERSON><PERSON> lebih banyak", "seeLess": "<PERSON><PERSON> lebih sedikit", "tokens": "Token:", "cache": "Cache:", "apiCost": "Biaya API:", "condenseContext": "Kondensasi konteks secara cerdas", "contextWindow": "Panjang Konteks:", "closeAndStart": "Tutup tugas dan mulai yang baru", "export": "Ekspor riwayat tugas", "share": "Bagikan tugas", "delete": "<PERSON><PERSON> (Shift + Klik untuk lewati konfirmasi)", "shareWithOrganization": "Bagikan dengan organisasi", "shareWithOrganizationDescription": "Hanya anggota organisasi Anda yang dapat mengakses", "sharePublicly": "Bagikan secara publik", "sharePubliclyDescription": "Siapa pun dengan tautan dapat mengakses", "connectToCloud": "Hubungkan ke Cloud", "connectToCloudDescription": "Masuk ke zhanlu Cloud untuk berbagi tugas", "sharingDisabledByOrganization": "Berbagi dinonaktifkan oleh organisasi", "shareSuccessOrganization": "Tautan organisasi disalin ke clipboard", "shareSuccessPublic": "Tautan publik disalin ke clipboard"}, "history": {"title": "Riwayat"}, "unpin": "Lepas Pin", "pin": "<PERSON>n", "retry": {"title": "<PERSON><PERSON>", "tooltip": "Coba operasi lagi"}, "startNewTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> tug<PERSON> baru"}, "reportBug": {"title": "Laporkan Bug"}, "proceedAnyways": {"title": "Lanju<PERSON><PERSON>", "tooltip": "Lanjutkan saat perintah di<PERSON>an"}, "save": {"title": "Simpan", "tooltip": "<PERSON><PERSON><PERSON> per<PERSON> pesan"}, "tokenProgress": {"availableSpace": "<PERSON>uang tersedia: {{amount}} token", "tokensUsed": "Token digunakan: {{used}} dari {{total}}", "reservedForResponse": "Dicadangkan untuk respons model: {{amount}} token"}, "reject": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> aksi ini"}, "completeSubtaskAndReturn": "Selesaikan Subtugas dan <PERSON>", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Setujui aksi ini"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "deny": {"title": "<PERSON><PERSON>"}}, "runCommand": {"title": "Jalankan <PERSON>ah", "tooltip": "Eks<PERSON><PERSON><PERSON> perintah ini"}, "proceedWhileRunning": {"title": "Lanjutkan Saat Berjalan", "tooltip": "Lanjutkan meskipun ada peringatan"}, "killCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Hentikan perintah saat ini"}, "resumeTask": {"title": "<PERSON>n<PERSON><PERSON><PERSON>", "tooltip": "Lanjutkan tugas saat ini"}, "terminate": {"title": "Hentikan", "tooltip": "<PERSON><PERSON><PERSON> tugas saat ini"}, "cancel": {"title": "<PERSON><PERSON>", "tooltip": "Batalkan operasi saat ini"}, "scrollToBottom": "<PERSON><PERSON><PERSON> ke bawah chat", "about": "<PERSON><PERSON><PERSON>, refa<PERSON><PERSON>, dan debug kode dengan bantuan AI.<br />Lihat <DocsLink>dokumentasi</DocsLink> kami untuk mempelajari lebih lanjut.", "onboarding": "Daftar tugas di workspace ini kosong.", "zhanluTips": {"boomerangTasks": {"title": "Orkestrasi Tugas", "description": "Bagi tugas menjadi bagian-bagian kecil yang dapat dikelola"}, "stickyModels": {"title": "Model Sticky", "description": "Setiap mode mengingat model te<PERSON><PERSON> yang kamu gunakan"}, "tools": {"title": "Tools", "description": "Izinkan AI menyelesaikan masalah dengan browsing web, men<PERSON><PERSON><PERSON> perintah, dan la<PERSON><PERSON>"}, "customizableModes": {"title": "Mode yang Dapat Disesuaikan", "description": "<PERSON>a k<PERSON>us dengan perilaku dan model yang ditu<PERSON> sendiri"}}, "selectMode": "Pilih mode untuk interaksi", "selectApiConfig": "Pilih konfigurasi API", "enhancePrompt": "Tingkatkan prompt dengan konteks tambahan", "enhancePromptDescription": "Tombol 'Tingkatkan Prompt' membantu memperbaiki prompt kamu dengan member<PERSON>n konteks tambahan, k<PERSON><PERSON><PERSON><PERSON>, atau pen<PERSON><PERSON>an ulang. Coba ketik prompt di sini dan klik tombol lagi untuk melihat cara kerjanya.", "modeSelector": {"title": "Mode", "marketplace": "Marketplace Mode", "settings": "Pengaturan Mode", "description": "<PERSON>a khusus yang menyesuaikan per<PERSON>."}, "addImages": "Tambahkan gambar ke pesan", "sendMessage": "<PERSON><PERSON>", "stopTts": "Hentikan text-to-speech", "typeMessage": "<PERSON><PERSON><PERSON> pesan...", "typeTask": "<PERSON><PERSON>, cari, tanya sesuatu", "addContext": "@ untuk menambah konteks, / untuk ganti mode", "dragFiles": "<PERSON>han shift untuk drag file", "dragFilesImages": "tahan shift untuk drag file/gambar", "errorReadingFile": "Error membaca file:", "noValidImages": "Tidak ada gambar valid yang diproses", "separator": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Edit...", "forNextMode": "untuk mode selanjutnya", "forPreviousMode": "untuk mode sebelumnya", "apiRequest": {"title": "Permintaan API", "failed": "Permintaan API Gagal", "streaming": "Permintaan API...", "cancelled": "Permintaan API Dibatalkan", "streamingFailed": "Streaming API Gagal"}, "checkpoint": {"initial": "Checkpoint Awal", "regular": "Checkpoint", "initializingWarning": "<PERSON><PERSON><PERSON> men<PERSON> checkpoint... <PERSON><PERSON> ini terlalu lama, kamu bisa menonaktifkan checkpoint di <settingsLink>pengaturan</settingsLink> dan restart tugas.", "menu": {"viewDiff": "<PERSON><PERSON>", "restore": "Pulihkan Checkpoint", "restoreFiles": "Pulihkan File", "restoreFilesDescription": "Mengembalikan file proyek kamu ke snapshot yang diambil pada titik ini.", "restoreFilesAndTask": "Pulihkan File & Tugas", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "cannotUndo": "Aksi ini tidak dapat di<PERSON>alk<PERSON>.", "restoreFilesAndTaskDescription": "Mengembalikan file proyek kamu ke snapshot yang diambil pada titik ini dan menghapus semua pesan setelah titik ini."}, "current": "Saat Ini"}, "contextCondense": {"title": "Konteks Dikondensasi", "condensing": "Mengondensasi konteks...", "errorHeader": "Gagal mengondensasi konteks", "tokens": "token"}, "instructions": {"wantsToFetch": "z<PERSON><PERSON> ingin mengambil instruksi detail untuk membantu tugas saat ini"}, "fileOperations": {"wantsToRead": "z<PERSON><PERSON> ingin membaca file ini:", "wantsToReadMultiple": "z<PERSON><PERSON> ingin membaca beberapa file:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> ingin membaca file ini dan {{count}} la<PERSON><PERSON>:", "wantsToReadOutsideWorkspace": "z<PERSON><PERSON> ingin membaca file ini di luar workspace:", "didRead": "zhanlu membaca file ini:", "wantsToEdit": "z<PERSON><PERSON> ingin mengedit file ini:", "wantsToEditOutsideWorkspace": "z<PERSON><PERSON> ingin mengedit file ini di luar workspace:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> ingin mengedit file konfigurasi yang dilindungi:", "wantsToApplyBatchChanges": "z<PERSON><PERSON> ingin menerapkan perubahan ke beberapa file:", "wantsToCreate": "z<PERSON><PERSON> ingin membuat file baru:", "wantsToSearchReplace": "zhan<PERSON> ingin mencari dan mengganti di file ini:", "didSearchReplace": "<PERSON><PERSON><PERSON> melakukan pencarian dan penggantian pada file ini:", "wantsToInsert": "z<PERSON><PERSON> ingin menyisipkan konten ke file ini:", "wantsToInsertWithLineNumber": "z<PERSON><PERSON> ingin menyisipkan konten ke file ini di baris {{lineNumber}}:", "wantsToInsertAtEnd": "z<PERSON><PERSON> ingin menambahkan konten ke akhir file ini:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> ingin melihat file tingkat atas di direktori ini:", "didViewTopLevel": "<PERSON><PERSON><PERSON> melihat file tingkat atas di direktori ini:", "wantsToViewRecursive": "z<PERSON><PERSON> ingin melihat semua file secara rekursif di direktori ini:", "didViewRecursive": "z<PERSON><PERSON> melihat semua file secara rekursif di direktori ini:", "wantsToViewDefinitions": "z<PERSON><PERSON> ingin melihat nama definisi source code yang digunakan di direktori ini:", "didViewDefinitions": "zhanlu melihat nama definisi source code yang digunakan di direktori ini:", "wantsToSearch": "z<PERSON><PERSON> ingin mencari direktori ini untuk <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> mencari direktori ini untuk <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "z<PERSON><PERSON> ingin mencari direktori ini (di luar workspace) untuk <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> mencari direktori ini (di luar workspace) untuk <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "z<PERSON><PERSON> ingin melihat file tingkat atas di direktori ini (di luar workspace):", "didViewTopLevelOutsideWorkspace": "zhanlu melihat file tingkat atas di direktori ini (di luar workspace):", "wantsToViewRecursiveOutsideWorkspace": "z<PERSON><PERSON> ingin melihat semua file secara rekursif di direktori ini (di luar workspace):", "didViewRecursiveOutsideWorkspace": "z<PERSON><PERSON> melihat semua file secara rekursif di direktori ini (di luar workspace):", "wantsToViewDefinitionsOutsideWorkspace": "z<PERSON><PERSON> ingin melihat nama definisi source code yang digunakan di direktori ini (di luar workspace):", "didViewDefinitionsOutsideWorkspace": "zhanlu melihat nama definisi source code yang digunakan di direktori ini (di luar workspace):"}, "codebaseSearch": {"wantsToSearch": "zhanlu ingin mencari codebase untuk <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> ingin mencari codebase untuk <code>{{query}}</code> di <code>{{path}}</code>:", "didSearch_one": "Ditemukan 1 hasil", "didSearch_other": "Di<PERSON><PERSON>n {{count}} hasil", "resultTooltip": "Skor kemiripan: {{score}} (klik untuk membuka file)"}, "commandOutput": "Output Perintah", "commandExecution": {"running": "<PERSON><PERSON><PERSON><PERSON>", "pid": "PID: {{pid}}", "exited": "Keluar ({{exitCode}})", "manageCommands": "<PERSON><PERSON><PERSON>", "commandManagementDescription": "Kelola izin perintah: Klik ✓ untuk mengizinkan eksekusi otomatis, ✗ untuk menolak eksekusi. Pola dapat diaktifkan/dinonaktifkan atau dihapus dari daftar. <settingsLink><PERSON>hat semua pengaturan</settingsLink>", "addToAllowed": "Tambahkan ke daftar yang di<PERSON>an", "removeFromAllowed": "<PERSON><PERSON> dari daftar yang di<PERSON>an", "addToDenied": "Tambahkan ke daftar yang ditolak", "removeFromDenied": "<PERSON><PERSON> dari daftar yang ditolak", "abortCommand": "Batalkan eksekusi per<PERSON>ah", "expandOutput": "Perluas output", "collapseOutput": "Ciutkan output", "expandManagement": "<PERSON><PERSON><PERSON> bagian manajemen perintah", "collapseManagement": "Ciutkan bagian manajemen perintah"}, "response": "Respons", "arguments": "Argumen", "mcp": {"wantsToUseTool": "zhanlu ingin menggunakan tool di server MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> ingin mengakses resource di server MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "z<PERSON><PERSON> ingin beralih ke mode {{mode}}", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> ingin beralih ke mode {{mode}} karena: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> beralih ke mode {{mode}}", "didSwitchWithReason": "<PERSON><PERSON><PERSON> beralih ke mode {{mode}} karena: {{reason}}"}, "subtasks": {"wantsToCreate": "z<PERSON><PERSON> ingin membuat subtugas baru dalam mode {{mode}}:", "wantsToFinish": "z<PERSON><PERSON> ingin menyelesaikan subtugas ini", "newTaskContent": "Instruksi Subtugas", "completionContent": "Subtugas Selesai", "resultContent": "<PERSON><PERSON>", "defaultResult": "<PERSON><PERSON><PERSON> lan<PERSON>kan ke tugas berikutnya.", "completionInstructions": "Subtugas selesai! Kamu bisa meninjau hasilnya dan menyarankan koreksi atau langkah selanjutnya. Jika semuanya terlihat baik, konfirmasi untuk mengembalikan hasil ke tugas induk."}, "questions": {"hasQuestion": "zhanlu punya pertanyaan:"}, "taskCompleted": "<PERSON><PERSON>", "error": "Error", "diffError": {"title": "<PERSON> <PERSON><PERSON><PERSON>"}, "troubleMessage": "<PERSON><PERSON><PERSON> mengalami masalah...", "powershell": {"issues": "Sepertinya kamu mengalami masalah Windows PowerShell, silakan lihat ini"}, "autoApprove": {"title": "Auto-approve:", "none": "Tidak Ada", "description": "Auto-approve memungkinkan zhanlu melakukan aksi tanpa meminta izin. Hanya aktifkan untuk aksi yang benar-benar kamu percayai. Konfigurasi lebih detail tersedia di <settingsLink>Pengaturan</settingsLink>.", "selectOptionsFirst": "<PERSON><PERSON>h setidaknya satu opsi di bawah untuk mengaktifkan persetujuan otomatis", "toggleAriaLabel": "<PERSON><PERSON><PERSON> otomatis", "disabledAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> otomatis din<PERSON> - pilih opsi terlebih dahulu"}, "announcement": {"title": "🎉 zhanlu {{version}} Dirilis", "description": "zhan<PERSON> {{version}} menghadirkan fitur-fitur baru yang kuat dan peningkatan signifikan untuk meningkatkan alur kerja pengembangan Anda.", "whatsNew": "<PERSON>", "feature1": "<bold><PERSON><PERSON><PERSON>gging Face</bold>: <PERSON><PERSON><PERSON> model open source yang luar biasa secara langsung melalui penyedia Hugging Face baru dengan integrasi yang mulus dan pemilihan model.", "feature2": "<bold><PERSON><PERSON><PERSON> Inline</bold>: <PERSON><PERSON><PERSON> otomatis dan penolakan baru untuk eksekusi perintah memberi Anda kontrol yang tepat atas operasi terminal dengan izin yang dapat disesuaikan.", "feature3": "<bold>Dukungan Aturan AGENTS.md</bold>: Menambahkan dukungan untuk file AGENTS.md standar komunitas di root proyek.", "hideButton": "Sembunyikan pen<PERSON>", "detailsDiscussLinks": "Dapatkan detail lebih lanjut dan bergabung dalam diskusi di <discordLink>Discord</discordLink> dan <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}}d"}, "followUpSuggest": {"copyToInput": "Salin ke input (sama dengan shift + klik)", "autoSelectCountdown": "<PERSON><PERSON><PERSON><PERSON> otomati<PERSON> da<PERSON> {{count}}dtk", "countdownDisplay": "{{count}}dtk"}, "browser": {"rooWantsToUse": "zhanlu ingin men<PERSON>kan browser:", "consoleLogs": "<PERSON><PERSON>", "noNewLogs": "(Tidak ada log baru)", "screenshot": "Screenshot browser", "cursor": "kursor", "navigation": {"step": "Langkah {{current}} dari {{total}}", "previous": "Sebelumnya", "next": "Selanjutnya"}, "sessionStarted": "<PERSON><PERSON>", "actions": {"title": "<PERSON><PERSON><PERSON>: ", "launch": "Luncurkan browser di {{url}}", "click": "<PERSON>lik ({{coordinate}})", "type": "<PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON> ke bawah", "scrollUp": "<PERSON><PERSON><PERSON> ke atas", "close": "Tutup browser"}}, "codeblock": {"tooltips": {"expand": "<PERSON><PERSON><PERSON> blok kode", "collapse": "Tutup blok kode", "enable_wrap": "Aktifkan word wrap", "disable_wrap": "Nonaktifkan word wrap", "copy_code": "<PERSON><PERSON> kode"}}, "systemPromptWarning": "PERINGATAN: Override system prompt kustom aktif. Ini dapat merusak fungsionalitas secara serius dan menyebabkan perilaku yang tidak terduga.", "profileViolationWarning": "Profil saat ini tidak kompatibel dengan pengaturan organisasi kamu", "shellIntegration": {"title": "Peringatan Eksekus<PERSON>", "description": "<PERSON>intah kamu dijalankan tanpa integrasi shell terminal VSCode. Untuk menekan peringatan ini kamu bisa menonaktifkan integrasi shell di bagian <strong>Terminal</strong> dari <settingsLink>pengaturan zhanlu</settingsLink> atau troubleshoot integrasi terminal VSCode menggunakan link di bawah.", "troubleshooting": "Klik di sini untuk dokumentasi integrasi shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Batas <PERSON> yang <PERSON> Otomatis Terca<PERSON>i", "description": "zhan<PERSON> telah mencapai batas {{count}} permintaan API yang disetujui otomatis. Apakah kamu ingin mengatur ulang hitungan dan melanjutkan tugas?", "button": "Atur Ulang dan <PERSON>"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> siap", "indexing": "Mengindeks {{percentage}}%", "indexed": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON>rror in<PERSON>s", "status": "Status indeks"}, "versionIndicator": {"ariaLabel": "Versi {{version}} - Klik untuk melihat catatan rilis"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> <PERSON> segera hadir!", "description": "Jalankan agen jarak jauh di <PERSON>, aks<PERSON> tugas <PERSON>a dari mana saja, be<PERSON><PERSON><PERSON><PERSON> dengan orang lain, dan banyak lagi.", "joinWaitlist": "Bergabunglah dengan daftar tunggu untuk mendapatkan akses awal."}, "editMessage": {"placeholder": "Edit pesan <PERSON>..."}}