{"title": "Server MCP", "done": "Se<PERSON><PERSON>", "marketplace": "Marketplace MCP", "description": "<0>Model Context Protocol</0> memungkinkan komunikasi dengan server MCP yang berjalan secara lokal yang menyediakan tools dan resources tambahan untuk memperluas kemampuan zhanlu. Anda dapat menggunakan <1>server buatan komunitas</1> atau meminta zhanlu membuat tools baru yang spesifik untuk workflow Anda (misalnya, \"tambahkan tool yang mendapatkan dokumentasi npm terbaru\").", "instructions": "Instruksi", "enableToggle": {"title": "Aktifkan Server MCP", "description": "Nyalakan ini untuk membiarkan zhanlu menggunakan tools dari server MCP yang terhubung. Ini memberikan zhanlu lebih banyak kemampuan. Jika Anda tidak berencana menggunakan tools tambahan ini, matikan untuk membantu mengurangi biaya token API."}, "enableServerCreation": {"title": "Aktifkan Pembuatan Server MCP", "description": "Aktifkan ini agar zhanlu membantu Anda membangun server MCP kustom <1>baru</1>. <0>Pelajari tentang pembuatan server</0>", "hint": "Petunjuk: Untuk mengurangi biaya token API, nonaktifkan pengaturan ini ketika Anda tidak secara aktif meminta zhanlu untuk membuat server MCP baru."}, "editGlobalMCP": "Edit MCP Global", "editProjectMCP": "Edit MCP Proyek", "refreshMCP": "Refresh Server MCP", "learnMoreEditingSettings": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang mengedit file pengaturan MCP", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON>", "parameters": "Parameter", "noDescription": "Tidak ada <PERSON>", "togglePromptInclusion": "Aktifkan daya untuk meminta"}, "tabs": {"tools": "Tools", "resources": "Resources", "errors": "Error"}, "emptyState": {"noTools": "Tidak ada tools ditemukan", "noResources": "Tidak ada resources ditemukan", "noErrors": "Tidak ada error ditemukan"}, "networkTimeout": {"label": "Network Timeout", "description": "<PERSON><PERSON><PERSON> maksimum untuk menunggu respons server", "options": {"15seconds": "15 detik", "30seconds": "30 detik", "1minute": "1 menit", "5minutes": "5 menit", "10minutes": "10 menit", "15minutes": "15 menit", "30minutes": "30 menit", "60minutes": "60 menit"}}, "deleteDialog": {"title": "Hapus Server MCP", "description": "<PERSON><PERSON><PERSON><PERSON>a yakin ingin menghapus server MCP \"{{serverName}}\"? Aksi ini tidak dapat dibatalkan.", "cancel": "<PERSON><PERSON>", "delete": "Hapus"}, "serverStatus": {"retrying": "Mencoba lagi...", "retryConnection": "Coba Koneksi Lagi"}, "execution": {"running": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "error": "Error"}}