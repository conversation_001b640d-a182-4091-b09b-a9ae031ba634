{"recentTasks": "Tugas", "viewAll": "<PERSON><PERSON>", "tokens": "Token: ↑{{in}} ↓{{out}}", "cache": "Cache: +{{writes}} → {{reads}}", "apiCost": "Biaya API: ${{cost}}", "history": "Riwayat", "exitSelectionMode": "<PERSON><PERSON>ar Mode Seleksi", "enterSelectionMode": "Masuk Mode Seleksi", "done": "Se<PERSON><PERSON>", "searchPlaceholder": "Pencarian fuzzy riwayat...", "newest": "Terbaru", "oldest": "Terlama", "mostExpensive": "<PERSON><PERSON><PERSON>", "mostTokens": "Token Terbanyak", "mostRelevant": "<PERSON><PERSON>", "deleteTaskTitle": "<PERSON><PERSON> (Shift + Klik untuk lewati konfirmasi)", "tokensLabel": "Token:", "cacheLabel": "Cache:", "apiCostLabel": "Biaya API:", "copyPrompt": "Salin Prompt", "exportTask": "Ekspor Tugas", "deleteTask": "Hapus <PERSON>", "deleteTaskMessage": "A<PERSON><PERSON>h kamu yakin ingin menghapus tugas ini? Aksi ini tidak dapat dibatalkan.", "cancel": "<PERSON><PERSON>", "delete": "Hapus", "exitSelection": "<PERSON><PERSON><PERSON>", "selectionMode": "Mode Seleksi", "deselectAll": "Batalkan pilihan semua", "selectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "selectedItems": "Dipilih {{selected}}/{{total}} item", "clearSelection": "<PERSON><PERSON>", "deleteSelected": "<PERSON><PERSON> ya<PERSON>", "deleteTasks": "Hapus <PERSON>", "confirmDeleteTasks": "A<PERSON><PERSON>h kamu yakin ingin menghapus {{count}} tugas?", "deleteTasksWarning": "Tugas yang dihapus tidak dapat dipulihkan. Pastikan kamu ingin melanjutkan.", "deleteTaskFavoritedWarning": "Tugas ini telah ditandai sebagai favorit. Apakah kamu yakin ingin menghap<PERSON>?", "deleteTasksFavoritedWarning": "{{count}} tugas yang dipilih telah ditandai sebagai favorit. Apakah kamu yakin ingin menghap<PERSON>nya?", "deleteItems": "Hapus {{count}} Item", "workspace": {"prefix": "<PERSON><PERSON>:", "current": "Saat Ini", "all": "<PERSON><PERSON><PERSON>"}, "sort": {"prefix": "Urutkan:", "newest": "Terbaru", "oldest": "Terlama", "mostExpensive": "<PERSON><PERSON><PERSON>", "mostTokens": "Token Terbanyak", "mostRelevant": "<PERSON><PERSON>"}, "viewAllHistory": "<PERSON><PERSON> semua tugas"}