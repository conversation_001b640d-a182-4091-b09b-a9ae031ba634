{"errorBoundary": {"title": "<PERSON><PERSON><PERSON><PERSON>", "reportText": "Bantu kami meningkatkan dengan melaporkan kesalahan ini di", "githubText": "<PERSON><PERSON><PERSON> kami", "copyInstructions": "Salin dan tempel pesan kesalahan berikut untuk menyertakannya sebagai bagian dari laporan Anda:", "errorStack": "<PERSON>ack <PERSON>r:", "componentStack": "Stack <PERSON>:"}, "answers": {"yes": "Ya", "no": "Tidak", "cancel": "<PERSON><PERSON>", "remove": "Hapus", "keep": "Simpan"}, "number_format": {"thousand_suffix": "rb", "million_suffix": "jt", "billion_suffix": "m"}, "ui": {"search_placeholder": "Cari..."}, "mermaid": {"loading": "Membuat diagram mermaid...", "render_error": "Tidak Dapat Merender Diagram", "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "save": "Simpan Gambar", "viewCode": "<PERSON><PERSON>", "viewDiagram": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "Kode Mermaid"}, "tabs": {"diagram": "Diagram", "code": "<PERSON><PERSON>"}, "feedback": {"imageCopied": "Gambar disalin ke clipboard", "copyError": "Error menyalin gambar"}}, "file": {"errors": {"invalidDataUri": "Format data URI tidak valid", "copyingImage": "Error menyalin gambar: {{error}}", "openingImage": "Error membuka gambar: {{error}}", "pathNotExists": "Path tidak ada: {{path}}", "couldNotOpen": "Tidak dapat membuka file: {{error}}", "couldNotOpenGeneric": "Tidak dapat membuka file!"}, "success": {"imageDataUriCopied": "Data URI gambar disalin ke clipboard"}}, "confirmation": {"deleteMessage": "<PERSON><PERSON>", "deleteWarning": "Menghapus pesan ini akan menghapus semua pesan selanjutnya dalam percakapan. Apakah kamu ingin melanjutkan?", "editMessage": "<PERSON>", "editWarning": "Mengedit pesan ini akan menghapus semua pesan selanjutnya dalam percakapan. Apakah kamu ingin melanjutkan?", "proceed": "Lanjutkan"}}