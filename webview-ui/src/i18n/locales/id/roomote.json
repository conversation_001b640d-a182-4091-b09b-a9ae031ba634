{"title": "<PERSON><PERSON>", "description": "Buat tugas pengembangan jarak jauh dengan integrasi JIRA dan dukungan repositori GitLab.", "jira": {"title": "Koneksi JIRA", "description": "Terhubung ke sistem JIRA untuk mendapatkan informasi tiket. Koneksi ini opsional. Jika Anda tidak terhubung ke JIRA, diperluk<PERSON> instruksi khusus."}, "gitlab": {"title": "Koneksi GitLab", "description": "Terhubung ke sistem GitLab untuk memilih repositori kode dan cabang. Ini diperlukan karena agen jarak jauh harus menjalankan tugas di repositori kode."}, "customInstructions": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Jelaskan tugas yang ingin Anda jalankan oleh agen jarak jauh. Bidang ini diperlukan jika Anda tidak terhubung ke JIRA.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> instruksi tugas khusus Anda di sini..."}, "aiSettings": {"title": "Pengaturan AI", "description": "<PERSON><PERSON><PERSON> penyedia AI dan model untuk eks<PERSON><PERSON><PERSON> tugas."}, "createTask": "<PERSON><PERSON><PERSON>", "creating": "Membuat...", "error": {"gitlabRequired": "Koneksi GitLab diperlukan", "customInstructionsRequired": "Instruksi khusus diperlukan saat tidak terhubung ke JIRA", "createTaskFailed": "<PERSON><PERSON> membuat tugas"}, "info": {"taskCreated": "Tugas Roomote berhasil dibuat"}}