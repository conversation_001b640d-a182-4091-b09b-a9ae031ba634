{"title": "Roomote Ajanı", "description": "JIRA entegrasyonu ve GitLab depo desteği ile uzaktan geliştirme görevleri oluşturun.", "jira": {"title": "JIRA Bağlantısı", "description": "Bilet bilgilerini almak için JIRA sistemine bağlanın. Bu bağlantı isteğe bağlıdır. JIRA'ya bağ<PERSON><PERSON>, özel talimatlar gereklidir."}, "gitlab": {"title": "GitLab Bağlantısı", "description": "Kod deposu ve dalı seçmek için GitLab sistemine bağlanın. Bu gereklidir çünkü uzak ajan kod deposunda görevleri yürütmelidir."}, "customInstructions": {"title": "<PERSON><PERSON>", "description": "Uzak ajanın yürütmesini istediğiniz görevi tanımlayın. JIRA'ya bağlı değilseniz bu alan gereklidir.", "placeholder": "Özel görev talimatlarınızı buraya girin..."}, "aiSettings": {"title": "AI Ayarları", "description": "Görev yürütme için AI sağlayıcısı ve modeli seçin."}, "createTask": "Görev <PERSON>", "creating": "Oluşturuluyor...", "error": {"gitlabRequired": "GitLab bağlantısı gerekli", "customInstructionsRequired": "JIRA'ya bağlı olmadığında özel talimatlar gerekli", "createTaskFailed": "Görev oluşturulamadı"}, "info": {"taskCreated": "Roomote görevi başarıyla oluşturuldu"}}