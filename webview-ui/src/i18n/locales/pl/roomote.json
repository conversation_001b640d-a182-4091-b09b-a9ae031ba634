{"title": "Agent <PERSON><PERSON>", "description": "Twórz zadania zdalnego rozwoju z integracją JIRA i obsługą repozytorium GitLab.", "jira": {"title": "Połączenie JIRA", "description": "Połącz się z systemem JIRA, aby uzyskać informacje o biletach. To połączenie jest opcjonalne. <PERSON><PERSON><PERSON> nie jesteś połączony z JIRA, wymagane są niestandardowe instrukcje."}, "gitlab": {"title": "Połączenie GitLab", "description": "Połącz się z systemem GitLab, aby wybrać repozytorium kodu i gałąź. Jest to w<PERSON><PERSON><PERSON>, ponieważ zdalny agent musi wykonywać zadania w repozytorium kodu."}, "customInstructions": {"title": "Niestandardowe Instrukcje", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, aby wyk<PERSON><PERSON> z<PERSON>ny agent. To pole jest wym<PERSON><PERSON>, je<PERSON><PERSON> nie jesteś połączony z JIRA.", "placeholder": "Wprowadź tutaj swoje niestandardowe instrukcje zadania..."}, "aiSettings": {"title": "Ustawienia AI", "description": "<PERSON><PERSON><PERSON><PERSON> dostawcę AI i model do wykonywania zadań."}, "createTask": "Ut<PERSON><PERSON><PERSON>", "creating": "Tworzenie...", "error": {"gitlabRequired": "Wymagane połączenie GitLab", "customInstructionsRequired": "Niestandardowe instrukcje wymagane, gdy nie połączono z JIRA", "createTaskFailed": "Nie udało się utworzyć zadania"}, "info": {"taskCreated": "Zadanie Roomote zostało pomyślnie utworzone"}}