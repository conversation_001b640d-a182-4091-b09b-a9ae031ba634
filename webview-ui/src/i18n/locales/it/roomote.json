{"title": "<PERSON><PERSON>", "description": "Crea attività di sviluppo remoto con integrazione JIRA e supporto repository GitLab.", "jira": {"title": "Connessione JIRA", "description": "Connettiti al sistema JIRA per ottenere informazioni sui ticket. Questa connessione è opzionale. Se non sei connesso a JIRA, sono richieste istruzioni personalizzate."}, "gitlab": {"title": "Connessione GitLab", "description": "Connettiti al sistema GitLab per selezionare repository di codice e branch. Questo è richiesto perché l'agente remoto deve eseguire attività nel repository di codice."}, "customInstructions": {"title": "Istruzioni Personalizzate", "description": "Descrivi l'attività che vuoi che l'agente remoto esegua. Questo campo è richiesto se non sei connesso a JIRA.", "placeholder": "Inserisci qui le tue istruzioni di attività personalizzate..."}, "aiSettings": {"title": "Impostazioni AI", "description": "Seleziona il provider AI e il modello per l'esecuzione delle attività."}, "createTask": "<PERSON><PERSON>", "creating": "Creazione in corso...", "error": {"gitlabRequired": "Connessione GitLab richiesta", "customInstructionsRequired": "Istruzioni personalizzate richieste quando non connesso a JIRA", "createTaskFailed": "Impossibile creare l'attività"}, "info": {"taskCreated": "Attività Roomote creata con successo"}}