{"title": "zhanlu Marketplace", "tabs": {"installed": "Đã cài đặt", "settings": "Cài đặt", "browse": "<PERSON><PERSON><PERSON><PERSON>"}, "done": "<PERSON><PERSON><PERSON> th<PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "filters": {"search": {"placeholder": "T<PERSON>m kiếm các mục marketplace...", "placeholderMcp": "<PERSON><PERSON><PERSON> MCP...", "placeholderMode": "<PERSON><PERSON><PERSON> k<PERSON>m <PERSON> độ..."}, "type": {"label": "<PERSON><PERSON><PERSON> the<PERSON> lo<PERSON>:", "all": "<PERSON><PERSON><PERSON> c<PERSON> lo<PERSON>i", "mode": "<PERSON><PERSON> độ", "mcpServer": "<PERSON><PERSON><PERSON> chủ MCP"}, "sort": {"label": "<PERSON><PERSON><PERSON> xếp theo:", "name": "<PERSON><PERSON><PERSON>", "author": "Tác g<PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i"}, "tags": {"label": "<PERSON><PERSON><PERSON> theo thẻ:", "clear": "Xóa thẻ", "placeholder": "<PERSON><PERSON><PERSON><PERSON> để tìm kiếm và chọn thẻ...", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ phù hợp", "selected": "<PERSON><PERSON><PERSON> thị các mục có bất kỳ thẻ nào được chọn", "clickToFilter": "<PERSON><PERSON><PERSON><PERSON> vào thẻ để lọc mục"}, "none": "<PERSON><PERSON><PERSON><PERSON> có"}, "type-group": {"modes": "<PERSON><PERSON> độ", "mcps": "<PERSON><PERSON><PERSON> chủ MCP"}, "items": {"empty": {"noItems": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục marketplace nào", "withFilters": "<PERSON>h<PERSON> điều chỉnh bộ lọc của bạn", "noSources": "<PERSON><PERSON><PERSON> thêm nguồn trong tab Nguồn", "adjustFilters": "Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm của bạn", "clearAllFilters": "<PERSON><PERSON><PERSON> tất cả bộ lọc"}, "count": "<PERSON><PERSON><PERSON> thấy {{count}} mục", "components": "{{count}} thành phần", "matched": "{{count}} khớp", "refresh": {"button": "<PERSON><PERSON><PERSON>", "refreshing": "<PERSON><PERSON> làm mới...", "mayTakeMoment": "<PERSON><PERSON><PERSON><PERSON> này có thể mất một chút thời gian."}, "card": {"by": "bởi {{author}}", "from": "từ {{source}}", "install": "Cài đặt", "installProject": "Cài đặt", "installGlobal": "<PERSON><PERSON><PERSON> đặt (Toàn c<PERSON>)", "remove": "Xóa", "removeProject": "Xóa", "removeGlobal": "<PERSON><PERSON><PERSON> (Toàn <PERSON>)", "viewSource": "Xem", "viewOnSource": "<PERSON><PERSON> trên {{source}}", "noWorkspaceTooltip": "Mở không gian làm việc để cài đặt các mục marketplace", "installed": "Đã cài đặt", "removeProjectTooltip": "<PERSON><PERSON><PERSON> khỏi dự án hiện tại", "removeGlobalTooltip": "<PERSON><PERSON><PERSON> khỏi cấu hình toàn cục", "actionsMenuLabel": "<PERSON><PERSON><PERSON><PERSON> hành động"}, "removeFailed": "<PERSON><PERSON><PERSON><PERSON> thể xóa mục: {{error}}", "unknownError": "<PERSON><PERSON> xảy ra lỗi không xác định"}, "install": {"title": "Cài đặt {{name}}", "titleMode": "<PERSON><PERSON><PERSON> đặt Chế độ {{name}}", "titleMcp": "Cài đặt MCP {{name}}", "scope": "Phạm vi cài đặt", "project": "<PERSON><PERSON> án (kh<PERSON>ng gian làm việc hiện tại)", "global": "<PERSON><PERSON><PERSON> (tất cả không gian làm việc)", "method": "<PERSON><PERSON><PERSON><PERSON> thức cài đặt", "configuration": "<PERSON><PERSON><PERSON> h<PERSON>nh", "configurationDescription": "<PERSON><PERSON><PERSON> hình các tham số cần thiết cho máy chủ MCP này", "button": "Cài đặt", "successTitle": "{{name}} đã đư<PERSON> cài đặt", "successDescription": "Cài đặt hoàn tất thành công", "installed": "Cài đặt thành công!", "whatNextMcp": "Bây giờ bạn có thể cấu hình và sử dụng máy chủ MCP này. Nhấp vào biểu tượng MCP trong thanh bên để chuyển tab.", "whatNextMode": "Bây giờ bạn có thể sử dụng chế độ này. Nhấp vào biểu tượng Chế độ trong thanh bên để chuyển tab.", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "goToMcp": "<PERSON><PERSON> MCP", "goToModes": "<PERSON>i đến Cài đặt Chế độ", "moreInfoMcp": "<PERSON><PERSON> tài li<PERSON>u MCP {{name}}", "validationRequired": "<PERSON>ui lòng cung cấp giá trị cho {{paramName}}", "prerequisites": "<PERSON><PERSON><PERSON><PERSON> kiện tiên quyết"}, "sources": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>n Marketplace", "description": "<PERSON>h<PERSON><PERSON> các kho Git chứa các mục marketplace. <PERSON><PERSON>c kho này sẽ được tải khi duyệt marketplace.", "add": {"title": "<PERSON><PERSON><PERSON><PERSON>", "urlPlaceholder": "URL kho Git (ví dụ: https://github.com/username/repo)", "urlFormats": "<PERSON><PERSON><PERSON> dạng được hỗ trợ: HTTPS (https://github.com/username/repo), SS<PERSON> (**************:username/repo.git), hoặc giao thức Git (git://github.com/username/repo.git)", "namePlaceholder": "<PERSON><PERSON><PERSON> hiển thị (tối đa 20 ký tự)", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "current": {"title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "empty": "<PERSON><PERSON><PERSON> có nguồn nào đ<PERSON><PERSON><PERSON> cấu hình. Thêm nguồn để bắt đầu.", "refresh": "<PERSON><PERSON><PERSON> mới nguồn này", "remove": "<PERSON><PERSON><PERSON> ng<PERSON>"}, "errors": {"emptyUrl": "URL không đư<PERSON><PERSON> để trống", "invalidUrl": "Định dạng URL không hợp lệ", "nonVisibleChars": "URL chứa các ký tự không hiển thị khác ngoài khoảng trắng", "invalidGitUrl": "URL phải là URL kho Git hợp lệ (ví dụ: https://github.com/username/repo)", "duplicateUrl": "URL này đã có trong danh sách (khớp không phân biệt chữ hoa thường và khoảng trắng)", "nameTooLong": "<PERSON><PERSON>n ph<PERSON>i có 20 ký tự hoặc ít hơn", "nonVisibleCharsName": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> các ký tự không hiển thị khác ngoài khoảng trắng", "duplicateName": "<PERSON>ê<PERSON> nà<PERSON> đã được sử dụng (khớp không phân biệt chữ hoa thường và khoảng trắng)", "emojiName": "<PERSON>ý tự emoji có thể gây ra vấn đề hiển thị", "maxSources": "T<PERSON>i đa {{max}} nguồ<PERSON> đ<PERSON><PERSON><PERSON> phép"}}, "removeConfirm": {"mode": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> độ", "message": "Bạn có chắc chắn muốn xóa chế độ \"{{modeName}}\" không?", "rulesWarning": "<PERSON><PERSON> tác này cũng sẽ xóa mọi tệp quy tắc được liên kết cho chế độ này."}, "mcp": {"title": "<PERSON>óa m<PERSON> chủ MCP", "message": "Bạn có chắc chắn muốn xóa máy chủ MCP \"{{mcpName}}\" không?"}, "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Xóa"}, "footer": {"issueText": "Bạn tìm thấy vấn đề với mục marketplace hoặc có đề xuất cho mục mới? <0>Mở issue GitHub</0> để cho chúng tôi biết!"}}