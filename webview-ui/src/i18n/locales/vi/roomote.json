{"title": "T<PERSON>c n<PERSON>ote", "description": "<PERSON><PERSON><PERSON> c<PERSON>c tác vụ phát triển từ xa với tích hợp JIRA và hỗ trợ kho GitLab.", "jira": {"title": "<PERSON>ết nối JIRA", "description": "Kết nối với hệ thống JIRA để lấy thông tin vé. Kết nối này là tùy chọn. <PERSON><PERSON>u bạn không kết nối với JIRA, cần có hướng dẫn tùy chỉnh."}, "gitlab": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ết nối với hệ thống GitLab để chọn kho mã và nhánh. Điều này là bắt buộc vì tác nhân từ xa phải thực hiện các tác vụ trong kho mã."}, "customInstructions": {"title": "Hướng dẫn tùy chỉnh", "description": "<PERSON><PERSON> tả tác vụ bạn muốn tác nhân từ xa thực hiện. Trường này là bắt buộc nếu bạn không kết nối với JIRA.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn tác vụ tùy chỉnh của bạn tại đây..."}, "aiSettings": {"title": "Cài đặt AI", "description": "<PERSON><PERSON><PERSON> nhà cung cấp AI và mô hình để thực hiện tác vụ."}, "createTask": "Tạo Tác vụ", "creating": "<PERSON><PERSON> tạo...", "error": {"gitlabRequired": "<PERSON><PERSON><PERSON> k<PERSON>t n<PERSON>", "customInstructionsRequired": "<PERSON>ần hướng dẫn tùy chỉnh khi không kết nối với JIRA", "createTaskFailed": "<PERSON><PERSON><PERSON> t<PERSON>c vụ thất bại"}, "info": {"taskCreated": "<PERSON><PERSON><PERSON> vụ Roomote đã đư<PERSON>c tạo thành công"}}