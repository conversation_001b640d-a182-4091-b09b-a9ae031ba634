{"title": "<PERSON><PERSON>", "description": "Crie tarefas de desenvolvimento remoto com integração JIRA e suporte a repositório GitLab.", "jira": {"title": "Conexão JIRA", "description": "Conecte-se ao sistema JIRA para obter informações de tickets. Esta conexão é opcional. Se você não estiver conectado ao JIRA, instruções personalizadas são necessárias."}, "gitlab": {"title": "Conexão GitLab", "description": "Conecte-se ao sistema GitLab para selecionar repositório de código e branch. Isto é necessário porque o agente remoto deve executar tarefas no repositório de código."}, "customInstructions": {"title": "Instruções Personalizadas", "description": "Descreva a tarefa que você quer que o agente remoto execute. Este campo é obrigatório se você não estiver conectado ao JIRA.", "placeholder": "Digite suas instruções de tarefa personalizadas aqui..."}, "aiSettings": {"title": "Configurações de IA", "description": "Selecione o provedor de IA e modelo para execução de tarefas."}, "createTask": "<PERSON><PERSON><PERSON>", "creating": "Criando...", "error": {"gitlabRequired": "Conexão GitLab obrigatória", "customInstructionsRequired": "Instruções personalizadas obrigatórias quando não conectado ao JIRA", "createTaskFailed": "Falha ao criar tarefa"}, "info": {"taskCreated": "Tarefa Roomote criada com sucesso"}}