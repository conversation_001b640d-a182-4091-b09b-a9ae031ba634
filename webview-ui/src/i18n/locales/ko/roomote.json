{"title": "Roomote 에이전트", "description": "JIRA 통합 및 GitLab 저장소 지원으로 원격 개발 작업을 생성합니다.", "jira": {"title": "JIRA 연결", "description": "JIRA 시스템에 연결하여 이슈 정보를 가져옵니다. 이 연결은 선택사항입니다. JIRA에 연결되지 않은 경우 사용자 정의 지침이 필요합니다."}, "gitlab": {"title": "GitLab 연결", "description": "GitLab 시스템에 연결하여 코드 저장소와 브랜치를 선택합니다. 원격 에이전트가 코드 저장소에서 작업을 실행해야 하므로 이는 필수입니다."}, "customInstructions": {"title": "사용자 정의 지침", "description": "원격 에이전트가 실행할 작업을 설명합니다. JIRA에 연결되지 않은 경우 이 필드는 필수입니다.", "placeholder": "여기에 사용자 정의 작업 지침을 입력하세요..."}, "aiSettings": {"title": "AI 구성", "description": "작업 실행을 위한 AI 제공업체와 모델을 선택합니다."}, "createTask": "작업 생성", "creating": "생성 중...", "error": {"gitlabRequired": "GitLab 연결이 필요합니다", "customInstructionsRequired": "JIRA에 연결되지 않은 경우 사용자 정의 지침이 필요합니다", "createTaskFailed": "작업 생성에 실패했습니다"}, "info": {"taskCreated": "Roomote 작업이 성공적으로 생성되었습니다"}}