{"title": "zhanlu Marketplace", "tabs": {"installed": "설치됨", "settings": "설정", "browse": "찾아보기"}, "done": "완료", "refresh": "새로고침", "filters": {"search": {"placeholder": "Marketplace 아이템 검색...", "placeholderMcp": "MCP 검색...", "placeholderMode": "모드 검색..."}, "type": {"label": "유형별 필터:", "all": "모든 유형", "mode": "모드", "mcpServer": "MCP 서버"}, "sort": {"label": "정렬 기준:", "name": "이름", "author": "작성자", "lastUpdated": "마지막 업데이트"}, "tags": {"label": "태그별 필터:", "clear": "태그 지우기", "placeholder": "태그를 검색하고 선택하려면 입력하세요...", "noResults": "일치하는 태그가 없습니다", "selected": "선택된 태그 중 하나를 가진 아이템 표시", "clickToFilter": "태그를 클릭하여 아이템 필터링"}, "none": "없음"}, "type-group": {"modes": "모드", "mcps": "MCP 서버"}, "items": {"empty": {"noItems": "Marketplace 아이템을 찾을 수 없습니다", "withFilters": "필터를 조정해 보세요", "noSources": "소스 탭에서 소스를 추가해 보세요", "adjustFilters": "필터나 검색어를 조정해 보세요", "clearAllFilters": "모든 필터 지우기"}, "count": "{{count}}개 아이템 발견", "components": "{{count}}개 구성 요소", "matched": "{{count}}개 일치", "refresh": {"button": "새로고침", "refreshing": "새로고침 중...", "mayTakeMoment": "잠시 시간이 걸릴 수 있습니다."}, "card": {"by": "{{author}} 작성", "from": "{{source}}에서", "install": "설치", "installProject": "설치", "installGlobal": "설치 (전역)", "remove": "삭제", "removeProject": "삭제", "removeGlobal": "삭제 (전역)", "viewSource": "보기", "viewOnSource": "{{source}}에서 보기", "noWorkspaceTooltip": "Marketplace 아이템을 설치하려면 워크스페이스를 열어주세요", "installed": "설치됨", "removeProjectTooltip": "현재 프로젝트에서 삭제", "removeGlobalTooltip": "전역 설정에서 삭제", "actionsMenuLabel": "추가 작업"}, "removeFailed": "항목을 제거하지 못했습니다: {{error}}", "unknownError": "알 수 없는 오류가 발생했습니다"}, "install": {"title": "{{name}} 설치", "titleMode": "{{name}} 모드 설치", "titleMcp": "{{name}} MCP 설치", "scope": "설치 범위", "project": "프로젝트 (현재 워크스페이스)", "global": "전역 (모든 워크스페이스)", "method": "설치 방법", "configuration": "설정", "configurationDescription": "이 MCP 서버에 필요한 매개변수를 설정하세요", "button": "설치", "successTitle": "{{name}} 설치됨", "successDescription": "설치가 성공적으로 완료되었습니다", "installed": "성공적으로 설치되었습니다!", "whatNextMcp": "이제 이 MCP 서버를 설정하고 사용할 수 있습니다. 사이드바의 MCP 아이콘을 클릭하여 탭을 전환하세요.", "whatNextMode": "이제 이 모드를 사용할 수 있습니다. 사이드바의 모드 아이콘을 클릭하여 탭을 전환하세요.", "done": "완료", "goToMcp": "MCP 탭으로 이동", "goToModes": "모드 설정으로 이동", "moreInfoMcp": "{{name}} MCP 문서 보기", "validationRequired": "{{paramName}}에 대한 값을 입력해주세요", "prerequisites": "전제 조건"}, "sources": {"title": "Marketplace 소스 설정", "description": "Marketplace 아이템이 포함된 Git 저장소를 추가하세요. Marketplace를 탐색할 때 이러한 저장소가 가져와집니다.", "add": {"title": "새 소스 추가", "urlPlaceholder": "Git 저장소 URL (예: https://github.com/username/repo)", "urlFormats": "지원되는 형식: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git) 또는 Git 프로토콜 (git://github.com/username/repo.git)", "namePlaceholder": "표시 이름 (최대 20자)", "button": "소스 추가"}, "current": {"title": "현재 소스", "empty": "설정된 소스가 없습니다. 시작하려면 소스를 추가하세요.", "refresh": "이 소스 새로고침", "remove": "소스 삭제"}, "errors": {"emptyUrl": "URL은 비워둘 수 없습니다", "invalidUrl": "잘못된 URL 형식", "nonVisibleChars": "URL에 공백 외의 보이지 않는 문자가 포함되어 있습니다", "invalidGitUrl": "URL은 유효한 Git 저장소 URL이어야 합니다 (예: https://github.com/username/repo)", "duplicateUrl": "이 URL은 이미 목록에 있습니다 (대소문자 및 공백 무시됨)", "nameTooLong": "이름은 20자 이하여야 합니다", "nonVisibleCharsName": "이름에 공백 외의 보이지 않는 문자가 포함되어 있습니다", "duplicateName": "이 이름은 이미 사용 중입니다 (대소문자 및 공백 무시됨)", "emojiName": "이모지 문자는 표시 문제를 일으킬 수 있습니다", "maxSources": "최대 {{max}}개의 소스가 허용됩니다"}}, "removeConfirm": {"mode": {"title": "모드 제거", "message": "정말로 '{{modeName}}' 모드를 제거하시겠습니까?", "rulesWarning": "이렇게 하면 이 모드와 관련된 모든 규칙 파일도 제거됩니다."}, "mcp": {"title": "MCP 서버 제거", "message": "정말로 '{{mcpName}}' MCP 서버를 제거하시겠습니까?"}, "cancel": "취소", "confirm": "제거"}, "footer": {"issueText": "Marketplace 아이템에 문제가 있거나 새로운 아이템에 대한 제안이 있나요? <0>GitHub issue를 열어서</0> 알려주세요!"}}