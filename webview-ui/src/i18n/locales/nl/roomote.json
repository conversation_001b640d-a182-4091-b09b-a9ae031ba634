{"title": "Roomote Agent", "description": "Maak remote ontwikkelingstaken met JIRA-integratie en GitLab repository-ondersteuning.", "jira": {"title": "JIRA Verbinding", "description": "<PERSON><PERSON><PERSON><PERSON> met het JIRA-systeem om ticket-informatie op te halen. Deze verbinding is optioneel. Als u niet verbonden bent met JIRA, zijn aangepaste instructies vereist."}, "gitlab": {"title": "GitLab Verbinding", "description": "<PERSON><PERSON><PERSON><PERSON> met het GitLab-systeem om code repository en branch te selecteren. <PERSON><PERSON> is vereist omdat de remote agent taken moet uitvoeren in de code repository."}, "customInstructions": {"title": "Aangepaste Instructies", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> de taak die u wilt dat de remote agent uitvoert. Dit veld is vereist als u niet verbonden bent met JIRA.", "placeholder": "<PERSON><PERSON><PERSON> hier uw aangepaste taakinstructies in..."}, "aiSettings": {"title": "AI Instellingen", "description": "Selecteer de AI-provider en model voor taakuitvoering."}, "createTask": "Taak <PERSON>", "creating": "Aanmaken...", "error": {"gitlabRequired": "GitLab verbinding vereist", "customInstructionsRequired": "Aangepaste instructies vereist wanneer niet verbonden met JIRA", "createTaskFailed": "Taak aanma<PERSON> mislukt"}, "info": {"taskCreated": "Roomote taak succesvol aangemaakt"}}