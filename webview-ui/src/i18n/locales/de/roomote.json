{"title": "Ecloud Agent", "description": "Starten Sie einen Remote-Agent, der parallel arbeitet, in einer isolierten Umgebung, die auch dann weiterl<PERSON>t, wenn Sie Ihren Laptop ausschalten.", "jira": {"title": "Mit JIRA verbinden", "description": "Optional: Importieren Sie Aufgabendetails aus JIRA-Tickets.", "connect": "Mit JIRA verbinden", "connected": "JIRA Verbunden", "disconnect": "<PERSON><PERSON><PERSON>"}, "gitlab": {"title": "Mit GitLab verbinden", "description": "Erforderlich: Wählen Sie Repository und Branch aus, an dem der Agent arbeiten soll.", "connect": "Mit GitLab verbinden", "connected": "GitLab Verbunden", "disconnect": "<PERSON><PERSON><PERSON>"}, "customInstructions": {"placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, was der Agent erreichen soll..."}, "aiSettings": {"title": "KI-Konfiguration", "description": "Wählen Sie KI-Anbieter und Modell für die Aufgabenausführung."}, "createTask": "Agent <PERSON><PERSON>", "creating": "Startet...", "error": {"gitlabRequired": "GitLab-Verbindung ist erforderlich", "customInstructionsRequired": "Aufgabenbeschreibung ist erforderlich, wenn nicht mit JIRA verbunden", "createTaskFailed": "Agent kon<PERSON> nicht gestartet werden"}, "info": {"taskCreated": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gestartet"}}