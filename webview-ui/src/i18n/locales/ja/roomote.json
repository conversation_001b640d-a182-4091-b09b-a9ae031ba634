{"title": "Ecloud Agent", "description": "ラップトップの電源を切っても継続して動作する、分離された環境で並行して作業するリモートエージェントを起動します。", "jira": {"title": "JIRAに接続", "description": "オプション：JIRA課題からタスク詳細をインポートします。", "connect": "JIRAに接続", "connected": "JIRA接続済み", "disconnect": "接続解除"}, "gitlab": {"title": "GitLabに接続", "description": "必須：エージェントが作業するリポジトリとブランチを選択します。", "connect": "GitLabに接続", "connected": "GitLab接続済み", "disconnect": "接続解除"}, "customInstructions": {"placeholder": "エージェントに達成してほしいことを説明してください..."}, "aiSettings": {"title": "AI設定", "description": "タスク実行用のAIプロバイダーとモデルを選択してください。"}, "createTask": "エージェント起動", "creating": "起動中...", "error": {"gitlabRequired": "GitLab接続が必要です", "customInstructionsRequired": "JIRAに接続していない場合、タスク説明が必要です", "createTaskFailed": "エージェントの起動に失敗しました"}, "info": {"taskCreated": "エージェントが正常に起動されました"}}