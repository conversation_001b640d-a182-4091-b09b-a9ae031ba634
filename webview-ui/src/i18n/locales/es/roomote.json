{"title": "Ecloud Agent", "description": "Inicie un agente remoto para trabajar en paralelo, en un entorno aislado que seguirá funcionando, incluso cuando apague su portátil.", "jira": {"title": "Conectar a JIRA", "description": "Opcional: Importar detalles de tareas desde tickets de JIRA.", "connect": "Conectar a JIRA", "connected": "JIRA Conectado", "disconnect": "Desconectar"}, "gitlab": {"title": "Conectar a GitLab", "description": "Requerido: Seleccione el repositorio y rama en los que trabajará el agente.", "connect": "Conectar a GitLab", "connected": "GitLab <PERSON>ectado", "disconnect": "Desconectar"}, "customInstructions": {"placeholder": "Describa lo que quiere que logre el agente..."}, "aiSettings": {"title": "Configuración de IA", "description": "Seleccione el proveedor de IA y el modelo para la ejecución de tareas."}, "createTask": "Lanzar Agente", "creating": "Lanzando...", "error": {"gitlabRequired": "Se requiere conexión GitLab", "customInstructionsRequired": "Se requiere descripción de la tarea cuando no está conectado a JIRA", "createTaskFailed": "Error al lanzar el agente"}, "info": {"taskCreated": "Agente lanzado exitosamente"}}