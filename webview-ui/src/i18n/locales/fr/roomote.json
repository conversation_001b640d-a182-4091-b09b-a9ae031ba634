{"title": "Ecloud Agent", "description": "Lancez un agent distant pour travailler en parallèle, dans un environnement isolé qui continuera à fonctionner, même lorsque vous éteignez votre ordinateur portable.", "jira": {"title": "Se connecter à JIRA", "description": "Optionnel : Importer les détails des tâches depuis les tickets JIRA.", "connect": "Se connecter à JIRA", "connected": "JIRA Connecté", "disconnect": "Déconnecter"}, "gitlab": {"title": "Se connecter à GitLab", "description": "Requis : Sé<PERSON><PERSON>ner le référentiel et la branche sur lesquels l'agent doit travailler.", "connect": "Se connecter à GitLab", "connected": "GitLab Connecté", "disconnect": "Déconnecter"}, "customInstructions": {"placeholder": "Décrivez ce que vous voulez que l'agent accomplisse..."}, "aiSettings": {"title": "Configuration IA", "description": "Sélectionnez le fournisseur IA et le modèle pour l'exécution des tâches."}, "createTask": "Lancer l'Agent", "creating": "Lancement...", "error": {"gitlabRequired": "La connexion GitLab est requise", "customInstructionsRequired": "La description de la tâche est requise lorsque vous n'êtes pas connecté à JIRA", "createTaskFailed": "Échec du lancement de l'agent"}, "info": {"taskCreated": "Agent la<PERSON><PERSON> avec succès"}}