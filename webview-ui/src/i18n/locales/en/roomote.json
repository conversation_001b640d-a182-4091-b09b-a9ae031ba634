{"title": "Ecloud Agent", "description": "Kick off a remote agent to work in parallel, in an isolated environment that will keep running, even when you shut off your laptop.", "jira": {"title": "Connect to JIRA", "description": "Optional: Import task details from JIRA issues.", "connect": "Connect to JIRA", "connected": "JIRA Connected", "disconnect": "Disconnect"}, "gitlab": {"title": "Connect to GitLab", "description": "Required: Select repository and branch for the agent to work on.", "connect": "Connect to GitLab", "connected": "GitLab Connected", "disconnect": "Disconnect"}, "customInstructions": {"placeholder": "Describe what you want the agent to accomplish..."}, "aiSettings": {"title": "AI Configuration", "description": "Select AI provider and model for task execution."}, "createTask": "Launch Agent", "creating": "Launching...", "error": {"gitlabRequired": "GitLab connection is required", "customInstructionsRequired": "Task description is required when not connected to JIRA", "createTaskFailed": "Failed to launch agent"}, "info": {"taskCreated": "Agent launched successfully"}}