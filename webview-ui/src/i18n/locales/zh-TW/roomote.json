{"title": "Roomote 遠程代理", "description": "創建支持 JIRA 集成和 GitLab 倉庫的遠程開發任務。", "jira": {"title": "JIRA 連接", "description": "連接到 JIRA 系統以獲取工單信息。此連接是可選的，如果不連接 JIRA，則需要輸入自定義指令。"}, "gitlab": {"title": "GitLab 連接", "description": "連接到 GitLab 系統以選擇代碼倉庫和分支。這是必需的，因為遠程代理需要在代碼倉庫中執行任務。"}, "customInstructions": {"title": "自定義指令", "description": "描述您希望遠程代理執行的任務。如果沒有連接 JIRA，此字段為必填項。", "placeholder": "請在此處輸入您的自定義任務指令..."}, "aiSettings": {"title": "AI 配置", "description": "選擇用於任務執行的 AI 提供商和模型。"}, "createTask": "創建任務", "creating": "創建中...", "error": {"gitlabRequired": "需要 GitLab 連接", "customInstructionsRequired": "未連接 JIRA 時需要自定義指令", "createTaskFailed": "創建任務失敗"}, "info": {"taskCreated": "Roomote 任務創建成功"}}