{"title": "Roomote एजेंट", "description": "JIRA एकीकरण और GitLab रिपॉजिटरी समर्थन के साथ रिमोट डेवलपमेंट कार्य बनाएं।", "jira": {"title": "JIRA कनेक्शन", "description": "टिकट जानकारी प्राप्त करने के लिए JIRA सिस्टम से कनेक्ट करें। यह कनेक्शन वैकल्पिक है। यदि आप JIRA से कनेक्ट नहीं हैं, तो कस्टम निर्देश आवश्यक हैं।"}, "gitlab": {"title": "<PERSON><PERSON><PERSON><PERSON> कनेक्शन", "description": "कोड रिपॉजिटरी और ब्रांच चुनने के लिए GitLab सिस्टम से कनेक्ट करें। यह आवश्यक है क्योंकि रिमोट एजेंट को कोड रिपॉजिटरी में कार्य निष्पादित करना होगा।"}, "customInstructions": {"title": "कस्टम निर्देश", "description": "उस कार्य का वर्णन करें जिसे आप रिमोट एजेंट से निष्पादित करवाना चाहते हैं। यदि आप JIRA से कनेक्ट नहीं हैं तो यह फ़ील्ड आवश्यक है।", "placeholder": "यहाँ अपने कस्टम कार्य निर्देश दर्ज करें..."}, "aiSettings": {"title": "AI सेटिंग्स", "description": "कार्य निष्पादन के लिए AI प्रदाता और मॉडल चुनें।"}, "createTask": "कार्य बनाएं", "creating": "बनाया जा रहा है...", "error": {"gitlabRequired": "<PERSON><PERSON><PERSON><PERSON> कनेक्शन आवश्यक है", "customInstructionsRequired": "JIRA से कनेक्ट नहीं होने पर कस्टम निर्देश आवश्यक हैं", "createTaskFailed": "कार्य बनाने में विफल"}, "info": {"taskCreated": "Roomote कार्य सफलतापूर्वक बनाया गया"}}