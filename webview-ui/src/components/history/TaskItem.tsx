import { memo } from "react"
import type { HistoryItem } from "@roo-code/types"

import { vscode } from "@/utils/vscode"
import { cn } from "@/lib/utils"
import { Checkbox } from "@/components/ui/checkbox"

import TaskItemHeader from "./TaskItemHeader"
import TaskItemFooter from "./TaskItemFooter"

interface DisplayHistoryItem extends HistoryItem {
	highlight?: string
}

interface TaskItemProps {
	item: DisplayHistoryItem
	variant: "compact" | "full"
	showWorkspace?: boolean
	isSelectionMode?: boolean
	isSelected?: boolean
	onToggleSelection?: (taskId: string, isSelected: boolean) => void
	onDelete?: (taskId: string) => void
	className?: string
}

const TaskItem = ({
	item,
	variant,
	showWorkspace = false,
	isSelectionMode = false,
	isSelected = false,
	onToggleSelection,
	onDelete,
	className,
}: TaskItemProps) => {
	const handleClick = () => {
		if (isSelectionMode && onToggleSelection) {
			onToggleSelection(item.id, !isSelected)
		} else {
			// 处理不同类型的任务
			if (item.taskType === "remote" && item.remoteTaskId) {
				// Remote Agent 任务：打开Remote Agent聊天界面
				vscode.postMessage({
					type: "openRemoteTaskChat",
					taskId: item.remoteTaskId,
					roomoteApiUrl: item.roomoteApiUrl,
					historyItem: item,
				})
			} else {
				// 本地任务：使用原有逻辑
				vscode.postMessage({ type: "showTaskWithId", text: item.id })
			}
		}
	}

	const isCompact = variant === "compact"

	return (
		<div
			key={item.id}
			data-testid={`task-item-${item.id}`}
			className={cn(
				"cursor-pointer group bg-vscode-editor-background rounded relative overflow-hidden hover:border-vscode-toolbar-hoverBackground/60",
				className,
			)}
			onClick={handleClick}>
			<div className="flex gap-2 p-3">
				{/* Selection checkbox - only in full variant */}
				{!isCompact && isSelectionMode && (
					<div
						className="task-checkbox mt-1"
						onClick={(e) => {
							e.stopPropagation()
						}}>
						<Checkbox
							checked={isSelected}
							onCheckedChange={(checked: boolean) => onToggleSelection?.(item.id, checked === true)}
							variant="description"
						/>
					</div>
				)}

				<div className="flex-1 min-w-0">
					{/* Header with metadata */}
					<TaskItemHeader item={item} isSelectionMode={isSelectionMode} onDelete={onDelete} />

					{/* Task content */}
					<div className="flex items-start gap-2">
						{/* 任务类型标识 */}
						{item.taskType === "remote" && (
							<div className="shrink-0 mt-0.5">
								<div className="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-600/20 text-blue-400 rounded text-xs font-medium border border-blue-600/30">
									<span className="codicon codicon-cloud scale-75"></span>
									Remote
								</div>
							</div>
						)}
						<div
							className={cn("overflow-hidden whitespace-pre-wrap text-vscode-foreground text-ellipsis", {
								"text-base line-clamp-3": !isCompact,
								"line-clamp-2": isCompact,
							})}
							data-testid="task-content"
							{...(item.highlight ? { dangerouslySetInnerHTML: { __html: item.highlight } } : {})}>
							{item.highlight ? undefined : item.task}
						</div>
					</div>

					{/* Remote Agent 配置信息 */}
					{item.taskType === "remote" && item.remoteConfig && !isCompact && (
						<div className="mt-2 text-xs text-vscode-descriptionForeground">
							<div className="flex flex-wrap gap-3">
								{item.remoteConfig.mode && <span>模式: {item.remoteConfig.mode}</span>}
								{item.remoteConfig.profileName && <span>配置: {item.remoteConfig.profileName}</span>}
								{item.remoteConfig.jiraIssue && <span>JIRA: {item.remoteConfig.jiraIssue}</span>}
								{item.remoteConfig.gitlabProject && (
									<span>GitLab: {item.remoteConfig.gitlabProject}</span>
								)}
							</div>
						</div>
					)}

					{/* Task Item Footer */}
					<TaskItemFooter item={item} variant={variant} isSelectionMode={isSelectionMode} />

					{/* Workspace info */}
					{showWorkspace && item.workspace && (
						<div className="flex flex-row gap-1 text-vscode-descriptionForeground text-xs mt-1">
							<span className="codicon codicon-folder scale-80" />
							<span>{item.workspace}</span>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}

export default memo(TaskItem)
