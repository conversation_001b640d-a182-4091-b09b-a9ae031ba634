import { HTMLAttributes } from "react"
import { useAppTranslation } from "@/i18n/TranslationContext"
import { ArrowRightToLine } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui"

import { SetCachedStateField } from "./types"
import { SectionHeader } from "./SectionHeader"
import { Section } from "./Section"

type CompletionSettingsProps = HTMLAttributes<HTMLDivElement> & {
	completionDebounceTime?: number
	completionNumber?: number
	inlineCompletionGranularity?: string
	multipleLineCompletion?: string
	maxTokensCompletion?: number
	setCachedStateField: SetCachedStateField<
		| "completionDebounceTime"
		| "completionNumber"
		| "inlineCompletionGranularity"
		| "multipleLineCompletion"
		| "maxTokensCompletion"
	>
}

export const CompletionSettings = ({
	completionDebounceTime,
	completionNumber,
	inlineCompletionGranularity,
	multipleLineCompletion,
	maxTokensCompletion,
	setCachedStateField,
	className,
	...props
}: CompletionSettingsProps) => {
	const { t } = useAppTranslation()

	return (
		<div className={cn("flex flex-col gap-2", className)} {...props}>
			<SectionHeader description={t("settings:completion.description")}>
				<div className="flex items-center gap-2">
					<ArrowRightToLine className="w-4" />
					<div>{t("settings:sections.completion")}</div>
				</div>
			</SectionHeader>

			<Section>
				<div className="space-y-4">
					{/* 补全触发延迟 */}
					<div>
						<label className="block font-medium mb-2">{t("settings:completion.debounceTime.label")}</label>
						<Select
							value={String(completionDebounceTime ?? 500)}
							onValueChange={(value) => setCachedStateField("completionDebounceTime", Number(value))}>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="500">500ms</SelectItem>
								<SelectItem value="1000">1000ms</SelectItem>
								<SelectItem value="1500">1500ms</SelectItem>
							</SelectContent>
						</Select>
						<div className="text-vscode-descriptionForeground text-sm mt-1">
							{t("settings:completion.debounceTime.description")}
						</div>
					</div>

					{/* 补全候选数量 */}
					<div>
						<label className="block font-medium mb-2">{t("settings:completion.number.label")}</label>
						<Select
							value={String(completionNumber ?? 1)}
							onValueChange={(value) => setCachedStateField("completionNumber", Number(value))}>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="1">1</SelectItem>
								<SelectItem value="2">2</SelectItem>
								<SelectItem value="3">3</SelectItem>
							</SelectContent>
						</Select>
						<div className="text-vscode-descriptionForeground text-sm mt-1">
							{t("settings:completion.number.description")}
						</div>
					</div>

					{/* 补全粒度偏好 */}
					<div>
						<label className="block font-medium mb-2">{t("settings:completion.granularity.label")}</label>
						<Select
							value={inlineCompletionGranularity ?? "均衡"}
							onValueChange={(value) => setCachedStateField("inlineCompletionGranularity", value)}>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="单行">{t("settings:completion.granularity.singleRow")}</SelectItem>
								<SelectItem value="一次性最大化">
									{t("settings:completion.granularity.oneTimeMaximization")}
								</SelectItem>
								<SelectItem value="均衡">{t("settings:completion.granularity.balanced")}</SelectItem>
							</SelectContent>
						</Select>
						<div className="text-vscode-descriptionForeground text-sm mt-1">
							{t("settings:completion.granularity.description")}
						</div>
					</div>

					{/* 多行补全方式 */}
					<div>
						<label className="block font-medium mb-2">
							{t("settings:completion.multipleLineCompletion.label")}
						</label>
						<Select
							value={multipleLineCompletion ?? "自动补全"}
							onValueChange={(value) => setCachedStateField("multipleLineCompletion", value)}>
							<SelectTrigger className="w-full">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="自动补全">
									{t("settings:completion.multipleLineCompletion.autoCompletion")}
								</SelectItem>
								<SelectItem value="触发补全">
									{t("settings:completion.multipleLineCompletion.triggerCompletion")}
								</SelectItem>
							</SelectContent>
						</Select>
						<div className="text-vscode-descriptionForeground text-sm mt-1">
							{t("settings:completion.multipleLineCompletion.description")}
						</div>
					</div>

					{/* 最大Token数 */}
					<div>
						<label className="block font-medium mb-2">{t("settings:completion.maxTokens.label")}</label>
						<Input
							type="number"
							min="1"
							max="1000"
							value={maxTokensCompletion ?? 64}
							onChange={(e) => setCachedStateField("maxTokensCompletion", Number(e.target.value))}
							className="w-full"
						/>
						<div className="text-vscode-descriptionForeground text-sm mt-1">
							{t("settings:completion.maxTokens.description")}
						</div>
					</div>
				</div>
			</Section>
		</div>
	)
}
