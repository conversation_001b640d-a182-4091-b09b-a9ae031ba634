import React, { useState, useEffect } from "react"
import { VSCodeButton, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { vscode } from "@src/utils/vscode"

interface RoomoteSettingsProps {
	roomoteApiUrl?: string
	setRoomoteApiUrl: (url: string) => void
	areSettingsCommitted: boolean
	className?: string
}

export const RoomoteSettings = ({ roomoteApiUrl, setRoomoteApiUrl, areSettingsCommitted }: RoomoteSettingsProps) => {
	const [isValidating, setIsValidating] = useState(false)
	const [isValid, setIsValid] = useState<boolean | null>(null)
	const [error, setError] = useState<string | null>(null)

	// 验证 Roomote API URL (通过后端)
	const validateRoomoteUrl = (url: string) => {
		if (!url) return

		setIsValidating(true)
		setError(null)

		// 通过vscode.postMessage发送验证请求到后端
		vscode.postMessage({
			type: "validateRoomoteApiUrl",
			url: url,
		})
	}

	// 监听来自后端的验证响应
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data

			if (message.type === "roomoteValidationResult") {
				setIsValidating(false)

				if (message.payload.success) {
					setIsValid(true)
					setError(null)
				} else {
					setIsValid(false)
					setError(message.payload.error || "验证失败")
				}
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [])

	// 当URL变化时重置验证状态
	useEffect(() => {
		setIsValid(null)
		setError(null)
	}, [roomoteApiUrl])

	// 当设置提交时进行验证
	useEffect(() => {
		if (roomoteApiUrl && areSettingsCommitted) {
			validateRoomoteUrl(roomoteApiUrl)
		}
	}, [areSettingsCommitted, roomoteApiUrl])

	return (
		<>
			<div className="text-sm text-vscode-descriptionForeground">
				<span
					className={`
						inline-block w-3 h-3 rounded-full mr-2
						${
							isValid === true
								? "bg-green-500"
								: isValid === false
									? "bg-red-500"
									: isValidating
										? "bg-yellow-500 animate-pulse"
										: "bg-gray-400"
						}
					`}></span>
				{isValidating ? "验证中..." : isValid === true ? "已连接" : isValid === false ? "连接失败" : "待验证"}
				{error ? ` - ${error}` : ""}
			</div>

			<div className="flex items-center gap-4 font-bold">
				<div>Remote Agent API 地址</div>
			</div>
			<div>
				<VSCodeTextField
					value={roomoteApiUrl || "http://localhost:3001"}
					onInput={(e: any) => setRoomoteApiUrl(e.target.value)}
					style={{ width: "100%" }}></VSCodeTextField>
			</div>

			<div className="flex gap-2">
				<VSCodeButton
					onClick={() => roomoteApiUrl && validateRoomoteUrl(roomoteApiUrl)}
					disabled={!roomoteApiUrl || isValidating}>
					{isValidating ? "验证中..." : "验证连接"}
				</VSCodeButton>
			</div>
		</>
	)
}
