import React, { useState, useC<PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Check, AlertCircle, GitBranch } from "lucide-react"
import { GitLabConfig, GitLabProject, GitLabBranch } from "@roo-code/types"
import { vscode } from "@/utils/vscode"

interface GitLabConnectionProps {
	onConfigChange: (config: GitLabConfig | null) => void
	onProjectChange: (project: GitLabProject | null) => void
	onBranchChange: (branch: string | null) => void
	onCancel?: () => void
	initialConfig?: GitLabConfig | null
}

export const GitLabConnection: React.FC<GitLabConnectionProps> = ({
	onConfigChange,
	onProjectChange,
	onBranch<PERSON>hange,
	onCancel,
	initialConfig,
}) => {
	const [config, setConfig] = useState<GitLabConfig>(() => ({
		url: initialConfig?.url || "http://gitlab.cmss.com",
		token: initialConfig?.token || "",
	}))

	const [isConnecting, setIsConnecting] = useState(false)
	const [isConnected, setIsConnected] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [userInfo, setUserInfo] = useState<any>(null)
	const [projects, setProjects] = useState<GitLabProject[]>([])
	const [selectedProject, setSelectedProject] = useState<GitLabProject | null>(null)
	const [branches, setBranches] = useState<GitLabBranch[]>([])
	const [selectedBranch, setSelectedBranch] = useState<string | null>(null)
	const [isLoadingProjects, setIsLoadingProjects] = useState(false)
	const [isLoadingBranches, setIsLoadingBranches] = useState(false)

	// 当 initialConfig 变化时更新本地配置
	useEffect(() => {
		if (initialConfig) {
			setConfig({
				url: initialConfig.url || "http://gitlab.cmss.com",
				token: initialConfig.token || "",
			})
			// 如果有完整的配置，尝试自动连接验证
			if (initialConfig.url && initialConfig.token) {
				setIsConnecting(true)
				vscode.postMessage({
					type: "gitlabConnect",
					gitlabConfig: initialConfig,
				})
			}
		} else {
			// 重置为默认配置
			setConfig({
				url: "http://gitlab.cmss.com",
				token: "",
			})
			setIsConnected(false)
			setUserInfo(null)
			setProjects([])
			setSelectedProject(null)
			setBranches([])
			setSelectedBranch(null)
			setError(null)
		}
	}, [initialConfig])

	const validateConfig = useCallback(() => {
		if (!config.url.trim()) {
			return "请输入 GitLab 服务器地址"
		}
		if (!config.token.trim()) {
			return "请输入 Personal Access Token"
		}
		return null
	}, [config])

	const connectToGitLab = useCallback(async () => {
		const validationError = validateConfig()
		if (validationError) {
			setError(validationError)
			return
		}

		setIsConnecting(true)
		setError(null)

		// 发送连接请求到后端
		vscode.postMessage({
			type: "gitlabConnect",
			gitlabConfig: config,
		})
	}, [config, validateConfig])

	const loadBranches = useCallback(
		async (project: GitLabProject) => {
			if (!isConnected) return

			setIsLoadingBranches(true)
			setError(null)

			// 发送获取分支请求到后端
			vscode.postMessage({
				type: "gitlabGetBranches",
				projectId: project.id,
			})
		},
		[isConnected],
	)

	const disconnect = useCallback(() => {
		// 发送断开连接请求到后端
		vscode.postMessage({
			type: "gitlabDisconnect",
		})

		// 立即更新本地状态
		setIsConnected(false)
		setUserInfo(null)
		setProjects([])
		setSelectedProject(null)
		setBranches([])
		setSelectedBranch(null)
		setError(null)
		onConfigChange(null)
		onProjectChange(null)
		onBranchChange(null)
	}, [onConfigChange, onProjectChange, onBranchChange])

	const handleInputChange = useCallback(
		(field: keyof GitLabConfig) => (e: React.ChangeEvent<HTMLInputElement>) => {
			setConfig((prev) => ({
				...prev,
				[field]: e.target.value,
			}))
			setError(null)
		},
		[],
	)

	const handleProjectSelect = useCallback(
		(projectId: string) => {
			const project = projects.find((p) => p.id.toString() === projectId)
			if (project) {
				setSelectedProject(project)
				setBranches([])
				setSelectedBranch(null)
				onProjectChange(project)
				onBranchChange(null)
				loadBranches(project)
			}
		},
		[projects, onProjectChange, onBranchChange, loadBranches],
	)

	const handleBranchSelect = useCallback(
		(branchName: string) => {
			setSelectedBranch(branchName)
			onBranchChange(branchName)
		},
		[onBranchChange],
	)

	// 监听来自后端的响应消息
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data

			switch (message.type) {
				case "gitlabConnectionResult":
					setIsConnecting(false)
					if (message.payload.success) {
						setIsConnected(true)
						setUserInfo(message.payload.userInfo)
						setError(null)
						onConfigChange(config)

						// 连接成功后获取项目列表
						setIsLoadingProjects(true)
						vscode.postMessage({
							type: "gitlabGetProjects",
						})
					} else {
						setIsConnected(false)
						setError(message.payload.error || "连接失败")
						onConfigChange(null)
					}
					break

				case "gitlabProjectsResult":
					setIsLoadingProjects(false)
					if (message.payload.success) {
						setProjects(message.payload.projects || [])
					} else {
						setError(message.payload.error || "获取项目失败")
						setIsConnected(false)
						onConfigChange(null)
					}
					break

				case "gitlabBranchesResult":
					setIsLoadingBranches(false)
					if (message.payload.success) {
						const branchList = message.payload.branches || []
						setBranches(branchList)

						// 自动选择默认分支
						const defaultBranch =
							branchList.find((b: GitLabBranch) => b.default) ||
							branchList.find((b: GitLabBranch) => b.name === selectedProject?.default_branch)
						if (defaultBranch) {
							setSelectedBranch(defaultBranch.name)
							onBranchChange(defaultBranch.name)
						}
					} else {
						setError(message.payload.error || "获取分支失败")
					}
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [config, onConfigChange, onBranchChange, selectedProject])

	return (
		<div className="w-full border rounded-lg p-4 space-y-4 bg-vscode-editor-background">
			<div className="space-y-2">
				<h3 className="flex items-center gap-2 text-lg font-semibold">
					<span className="codicon codicon-source-control"></span>
					GitLab 连接
					{isConnected && <Check className="w-4 h-4 text-green-500" />}
				</h3>
				<p className="text-sm text-vscode-descriptionForeground">
					连接到 GitLab 系统以选择代码仓库和分支。这是必需的，因为远程代理需要在代码仓库中执行任务。
				</p>
			</div>

			{!isConnected ? (
				<div className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="gitlab-url" className="text-sm font-medium">
							GitLab 服务器地址
						</label>
						<Input
							id="gitlab-url"
							type="url"
							placeholder="http://gitlab.cmss.com"
							value={config.url}
							onChange={handleInputChange("url")}
							disabled={isConnecting}
						/>
					</div>

					<div className="space-y-2">
						<label htmlFor="gitlab-token" className="text-sm font-medium">
							Personal Access Token
						</label>
						<Input
							id="gitlab-token"
							type="password"
							placeholder="您的 GitLab Personal Access Token"
							value={config.token}
							onChange={handleInputChange("token")}
							disabled={isConnecting}
						/>
						<p className="text-xs text-vscode-descriptionForeground">
							需要 api, read_repository, write_repository 权限
						</p>
					</div>

					{error && (
						<div className="flex items-center gap-2 p-3 text-sm text-red-400 bg-red-900/20 rounded border border-red-800">
							<AlertCircle className="h-4 w-4" />
							{error}
						</div>
					)}

					<div className="flex gap-2">
						<Button onClick={connectToGitLab} disabled={isConnecting} className="flex-1">
							{isConnecting ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									连接中...
								</>
							) : (
								"连接 GitLab"
							)}
						</Button>
						{onCancel && (
							<Button variant="outline" onClick={onCancel} disabled={isConnecting}>
								取消
							</Button>
						)}
					</div>
				</div>
			) : (
				<div className="space-y-4">
					<div className="flex items-center gap-2 p-3 text-sm text-green-400 bg-green-900/20 rounded border border-green-800">
						<Check className="h-4 w-4" />
						<div>
							<div className="font-medium">已连接到 GitLab</div>
							{userInfo && (
								<div className="text-xs text-vscode-descriptionForeground">
									用户: {userInfo.name || userInfo.username}
								</div>
							)}
						</div>
					</div>

					{/* 项目选择 */}
					<div className="space-y-2">
						<label className="text-sm font-medium">选择项目</label>
						{isLoadingProjects ? (
							<div className="flex items-center gap-2 text-sm text-vscode-descriptionForeground">
								<Loader2 className="w-4 h-4 animate-spin" />
								加载项目中...
							</div>
						) : projects.length > 0 ? (
							<Select value={selectedProject?.id.toString() || ""} onValueChange={handleProjectSelect}>
								<SelectTrigger>
									<SelectValue placeholder="选择项目..." />
								</SelectTrigger>
								<SelectContent>
									{projects.map((project) => (
										<SelectItem key={project.id} value={project.id.toString()}>
											{project.path_with_namespace}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						) : (
							<div className="text-sm text-vscode-descriptionForeground">没有找到可用的项目</div>
						)}
					</div>

					{/* 显示选中的项目信息 */}
					{selectedProject && (
						<div className="p-3 border border-vscode-input-border rounded bg-vscode-input-background">
							<div className="text-sm">
								<div className="font-medium">{selectedProject.name}</div>
								{selectedProject.description && (
									<div className="text-vscode-descriptionForeground text-xs mt-1">
										{selectedProject.description}
									</div>
								)}
								<div className="text-vscode-descriptionForeground text-xs mt-1">
									默认分支: {selectedProject.default_branch}
								</div>
							</div>
						</div>
					)}

					{/* 分支选择 */}
					{selectedProject && (
						<div className="space-y-2">
							<label className="text-sm font-medium flex items-center gap-2">
								<GitBranch className="w-4 h-4" />
								选择分支
							</label>
							{isLoadingBranches ? (
								<div className="flex items-center gap-2 text-sm text-vscode-descriptionForeground">
									<Loader2 className="w-4 h-4 animate-spin" />
									加载分支中...
								</div>
							) : branches.length > 0 ? (
								<Select value={selectedBranch || ""} onValueChange={handleBranchSelect}>
									<SelectTrigger>
										<SelectValue placeholder="选择分支..." />
									</SelectTrigger>
									<SelectContent>
										{branches.map((branch) => (
											<SelectItem key={branch.name} value={branch.name}>
												{branch.name}
												{branch.default && " (默认)"}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							) : (
								<div className="text-sm text-vscode-descriptionForeground">没有找到可用的分支</div>
							)}
						</div>
					)}

					{/* 显示选中的分支 */}
					{selectedBranch && (
						<div className="text-sm text-vscode-descriptionForeground">
							已选择分支: <span className="font-medium">{selectedBranch}</span>
						</div>
					)}

					<Button variant="outline" onClick={disconnect} className="w-full">
						断开连接
					</Button>
				</div>
			)}
		</div>
	)
}
