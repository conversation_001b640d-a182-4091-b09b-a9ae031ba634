import React, { useState, useCallback, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Loader2, Check, AlertCircle } from "lucide-react"
import { JiraConfig, JiraIssue } from "@roo-code/types"
import { vscode } from "@/utils/vscode"

interface JiraConnectionProps {
	onConfigChange: (config: JiraConfig | null) => void
	onIssuesFetched: (issues: JiraIssue[]) => void
	onCancel?: () => void
	initialConfig?: JiraConfig | null
}

export const JiraConnection: React.FC<JiraConnectionProps> = ({
	onConfigChange,
	onIssuesFetched,
	onCancel,
	initialConfig,
}) => {
	const [config, setConfig] = useState<JiraConfig>(() => ({
		url: initialConfig?.url || "http://jira.cmss.com",
		username: initialConfig?.username || "",
		password: initialConfig?.password || "",
	}))

	const [isConnecting, setIsConnecting] = useState(false)
	const [isConnected, setIsConnected] = useState(false)
	const [error, setError] = useState<string | null>(null)
	const [userInfo, setUserInfo] = useState<any>(null)
	const [recentIssues, setRecentIssues] = useState<JiraIssue[]>([])

	// 当 initialConfig 变化时更新本地配置
	useEffect(() => {
		if (initialConfig) {
			setConfig({
				url: initialConfig.url || "http://jira.cmss.com",
				username: initialConfig.username || "",
				password: initialConfig.password || "",
			})
			// 如果有完整的配置，尝试自动连接验证
			if (initialConfig.url && initialConfig.username && initialConfig.password) {
				setIsConnecting(true)
				vscode.postMessage({
					type: "jiraConnect",
					jiraConfig: initialConfig,
				})
			}
		} else {
			// 重置为默认配置
			setConfig({
				url: "http://jira.cmss.com",
				username: "",
				password: "",
			})
			setIsConnected(false)
			setUserInfo(null)
			setRecentIssues([])
			setError(null)
		}
	}, [initialConfig])

	const validateConfig = useCallback(() => {
		if (!config.url.trim()) {
			return "请输入 JIRA 服务器地址"
		}
		if (!config.username.trim()) {
			return "请输入用户名"
		}
		if (!config.password.trim()) {
			return "请输入密码"
		}
		return null
	}, [config])

	const connectToJira = useCallback(async () => {
		const validationError = validateConfig()
		if (validationError) {
			setError(validationError)
			return
		}

		setIsConnecting(true)
		setError(null)

		// 发送连接请求到后端
		vscode.postMessage({
			type: "jiraConnect",
			jiraConfig: config,
		})
	}, [config, validateConfig])

	const disconnect = useCallback(() => {
		// 发送断开连接请求到后端
		vscode.postMessage({
			type: "jiraDisconnect",
		})

		// 立即更新本地状态
		setIsConnected(false)
		setUserInfo(null)
		setRecentIssues([])
		setError(null)
		onConfigChange(null)
		onIssuesFetched([])
	}, [onConfigChange, onIssuesFetched])

	const handleInputChange = useCallback(
		(field: keyof JiraConfig) => (e: React.ChangeEvent<HTMLInputElement>) => {
			setConfig((prev) => ({
				...prev,
				[field]: e.target.value,
			}))
			setError(null)
		},
		[],
	)

	// 监听来自后端的响应消息
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data

			switch (message.type) {
				case "jiraConnectionResult":
					setIsConnecting(false)
					if (message.payload.success) {
						setIsConnected(true)
						setUserInfo(message.payload.userInfo)
						setError(null)
						onConfigChange(config)

						// 连接成功后获取工单列表
						vscode.postMessage({
							type: "jiraGetIssues",
						})
					} else {
						setIsConnected(false)
						setError(message.payload.error || "连接失败")
						onConfigChange(null)
					}
					break

				case "jiraIssuesResult":
					if (message.payload.success) {
						const issues = message.payload.issues || []
						setRecentIssues(issues)
						onIssuesFetched(issues)
					} else {
						setError(message.payload.error || "获取工单失败")
						setIsConnected(false)
						onConfigChange(null)
					}
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [config, onConfigChange, onIssuesFetched])

	return (
		<div className="w-full border rounded-lg p-4 space-y-4 bg-vscode-editor-background">
			<div className="space-y-2">
				<h3 className="flex items-center gap-2 text-lg font-semibold">
					<span className="codicon codicon-issues"></span>
					JIRA 连接
					{isConnected && <Check className="w-4 h-4 text-green-500" />}
				</h3>
				<p className="text-sm text-vscode-descriptionForeground">
					连接到 JIRA 系统以获取工单信息。连接是可选的，如果不连接 JIRA，需要输入自定义指令。
				</p>
			</div>

			{!isConnected ? (
				<div className="space-y-4">
					<div className="space-y-2">
						<label htmlFor="jira-url" className="text-sm font-medium">
							JIRA 服务器地址
						</label>
						<Input
							id="jira-url"
							type="url"
							placeholder="http://jira.cmss.com"
							value={config.url}
							onChange={handleInputChange("url")}
							disabled={isConnecting}
						/>
					</div>

					<div className="space-y-2">
						<label htmlFor="jira-username" className="text-sm font-medium">
							用户名
						</label>
						<Input
							id="jira-username"
							type="text"
							placeholder="您的 JIRA 用户名"
							value={config.username}
							onChange={handleInputChange("username")}
							disabled={isConnecting}
						/>
					</div>

					<div className="space-y-2">
						<label htmlFor="jira-password" className="text-sm font-medium">
							密码
						</label>
						<Input
							id="jira-password"
							type="password"
							placeholder="您的 JIRA 密码"
							value={config.password}
							onChange={handleInputChange("password")}
							disabled={isConnecting}
						/>
					</div>

					{error && (
						<div className="flex items-center gap-2 p-3 text-sm text-red-400 bg-red-900/20 rounded border border-red-800">
							<AlertCircle className="h-4 w-4" />
							{error}
						</div>
					)}

					<div className="flex gap-2">
						<Button onClick={connectToJira} disabled={isConnecting} className="flex-1">
							{isConnecting ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									连接中...
								</>
							) : (
								"连接 JIRA"
							)}
						</Button>
						{onCancel && (
							<Button variant="outline" onClick={onCancel} disabled={isConnecting}>
								取消
							</Button>
						)}
					</div>
				</div>
			) : (
				<div className="space-y-4">
					<div className="flex items-center gap-2 p-3 text-sm text-green-400 bg-green-900/20 rounded border border-green-800">
						<Check className="h-4 w-4" />
						<div>
							<div className="font-medium">已连接到 JIRA</div>
							{userInfo && (
								<div className="text-xs text-vscode-descriptionForeground">
									用户: {userInfo.displayName || config.username}
								</div>
							)}
						</div>
					</div>

					{recentIssues.length > 0 && (
						<div className="space-y-2">
							<h4 className="text-sm font-medium">最近的工单 ({recentIssues.length})</h4>
							<div className="space-y-1 max-h-32 overflow-y-auto">
								{recentIssues.slice(0, 5).map((issue) => (
									<div
										key={issue.key}
										className="flex items-center gap-2 p-2 text-xs bg-vscode-input-background rounded border border-vscode-input-border">
										<span className="font-medium">{issue.key}</span>
										<span className="text-vscode-descriptionForeground truncate">
											{issue.summary}
										</span>
									</div>
								))}
							</div>
						</div>
					)}

					<Button variant="outline" onClick={disconnect} className="w-full">
						断开连接
					</Button>
				</div>
			)}
		</div>
	)
}
