import React, { forwardRef, use<PERSON><PERSON>back, useState } from "react"
import DynamicTextArea from "react-textarea-autosize"
import { But<PERSON> } from "@/components/ui/button"
import { SelectDropdown, DropdownOptionType } from "@/components/ui"
import { Send, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

interface RoomoteTextAreaProps {
	value: string
	onChange: (value: string) => void
	onSend: () => void
	disabled: boolean
	placeholder: string
	apiProvider: string
	modelName: string
	onApiProviderChange: (provider: string) => void
	onModelNameChange: (model: string) => void
	onHeightChange?: (height: number) => void
}

const API_PROVIDERS = [
	{ id: "zhanlu", name: "湛卢", models: ["zhanluAI"] },
	{ id: "openrouter", name: "OpenRouter", models: ["gpt-4-turbo", "gpt-4o", "claude-3-sonnet", "claude-3-opus"] },
	{ id: "anthropic", name: "Anthropic", models: ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"] },
	{ id: "openai", name: "OpenAI", models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"] },
]

export const RoomoteTextArea = forwardRef<HTMLTextAreaElement, RoomoteTextAreaProps>(
	(
		{
			value,
			onChange,
			onSend,
			disabled,
			placeholder,
			apiProvider,
			modelName,
			onApiProviderChange,
			onModelNameChange,
			onHeightChange,
		},
		ref,
	) => {
		const [isFocused, setIsFocused] = useState(false)

		const handleInputChange = useCallback(
			(e: React.ChangeEvent<HTMLTextAreaElement>) => {
				onChange(e.target.value)
			},
			[onChange],
		)

		const handleKeyDown = useCallback(
			(event: React.KeyboardEvent<HTMLTextAreaElement>) => {
				const isComposing = event.nativeEvent?.isComposing ?? false

				if (event.key === "Enter" && !event.shiftKey && !isComposing) {
					event.preventDefault()
					if (!disabled && value.trim()) {
						onSend()
					}
				}
			},
			[disabled, value, onSend],
		)

		const currentProvider = API_PROVIDERS.find((p) => p.id === apiProvider)
		const availableModels = currentProvider?.models || []

		const handleProviderChange = useCallback(
			(providerId: string) => {
				onApiProviderChange(providerId)
				// 自动选择新提供商的第一个模型
				const provider = API_PROVIDERS.find((p) => p.id === providerId)
				if (provider && provider.models.length > 0) {
					onModelNameChange(provider.models[0])
				}
			},
			[onApiProviderChange, onModelNameChange],
		)

		return (
			<div
				className={cn(
					"relative",
					"flex",
					"flex-col",
					"gap-3",
					"bg-vscode-editor-background",
					"m-2 mt-1",
					"p-4",
					"border",
					"border-vscode-input-border",
					"rounded-lg",
					"w-[calc(100%-16px)]",
					"ml-auto",
					"mr-auto",
				)}>
				{/* 标题区域 */}
				<div className="space-y-2">
					<h3 className="flex items-center gap-2 text-lg font-semibold">
						<Sparkles className="w-5 h-5" />
						自定义指令
					</h3>
					<p className="text-sm text-vscode-descriptionForeground">
						描述您希望远程代理执行的任务。如果没有连接 JIRA，此字段为必填项。
					</p>
				</div>

				{/* 输入区域 */}
				<div className="relative">
					<DynamicTextArea
						ref={ref}
						value={value}
						onChange={handleInputChange}
						onFocus={() => setIsFocused(true)}
						onBlur={() => setIsFocused(false)}
						onKeyDown={handleKeyDown}
						onHeightChange={onHeightChange}
						placeholder={placeholder}
						minRows={4}
						maxRows={15}
						disabled={disabled}
						className={cn(
							"w-full",
							"text-vscode-input-foreground",
							"font-vscode-font-family",
							"text-vscode-editor-font-size",
							"leading-vscode-editor-line-height",
							"bg-vscode-input-background",
							"border",
							isFocused ? "border-vscode-focusBorder" : "border-vscode-input-border",
							"rounded-md",
							"py-3",
							"px-3",
							"resize-none",
							"outline-none",
							"transition-all",
							"duration-200",
							"placeholder:text-vscode-input-placeholderForeground",
							disabled && "opacity-50 cursor-not-allowed",
						)}
					/>
				</div>

				{/* 控制区域 */}
				<div className="flex items-center justify-between gap-3">
					{/* 左侧：模型配置 */}
					<div className="flex items-center gap-2 min-w-0 flex-1">
						<div className="flex items-center gap-2">
							<span className="text-sm font-medium whitespace-nowrap">提供商:</span>
							<SelectDropdown
								value={apiProvider}
								options={API_PROVIDERS.map((provider) => ({
									value: provider.id,
									label: provider.name,
									type: DropdownOptionType.ITEM,
								}))}
								onChange={handleProviderChange}
								disabled={disabled}
								triggerClassName="min-w-0"
							/>
						</div>

						<div className="flex items-center gap-2">
							<span className="text-sm font-medium whitespace-nowrap">模型:</span>
							<SelectDropdown
								value={modelName}
								options={availableModels.map((model) => ({
									value: model,
									label: model,
									type: DropdownOptionType.ITEM,
								}))}
								onChange={onModelNameChange}
								disabled={disabled || availableModels.length === 0}
								triggerClassName="min-w-0"
							/>
						</div>
					</div>

					{/* 右侧：发送按钮 */}
					<Button
						onClick={onSend}
						disabled={disabled || !value.trim()}
						size="sm"
						className="flex items-center gap-2 shrink-0">
						<Send className="w-4 h-4" />
						创建任务
					</Button>
				</div>

				{/* 提示信息 */}
				{!value && (
					<div className="text-xs text-vscode-descriptionForeground opacity-75">
						支持 Markdown 格式。按 Enter 创建任务，Shift+Enter 换行。
					</div>
				)}
			</div>
		)
	},
)

RoomoteTextArea.displayName = "RoomoteTextArea"

export default RoomoteTextArea
